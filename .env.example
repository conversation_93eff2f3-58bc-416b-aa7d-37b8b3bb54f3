# Environment Configuration for Gmail Push Notification System
# Copy this file to .env and fill in your actual values

# =============================================================================
# CLOUD RUN HANDLER CONFIGURATION
# =============================================================================

# FastAPI Backend URL (update with your actual FastAPI service URL)
FASTAPI_BASE_URL=http://localhost:8000

# Service token for authentication between Cloud Run and FastAPI
SERVICE_TOKEN=your-secure-service-token-here

# Google Cloud Project Configuration
GOOGLE_CLOUD_PROJECT=ai-email-bot-455814

# Pub/Sub Topic for Gmail notifications
PUBSUB_TOPIC=projects/ai-email-bot-455814/topics/email-notifications

# Environment (development, staging, production)
ENVIRONMENT=development

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# FASTAPI BACKEND CONFIGURATION
# =============================================================================

# Allow unauthenticated requests (development only)
ALLOW_UNAUTHENTICATED=true

# Gemini AI API Key
GEMINI_API_KEY=your-gemini-api-key-here

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================

# Path to Firebase service account credentials JSON file
GOOGLE_APPLICATION_CREDENTIALS=./backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json

# Firebase Storage Bucket
FIREBASE_STORAGE_BUCKET=ai-email-bot-455814.appspot.com

# =============================================================================
# GMAIL API CONFIGURATION
# =============================================================================

# Path to Gmail API client secrets JSON file
GMAIL_CREDENTIALS_JSON_PATH=./backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Webhook URL for purchase order notifications
WEBHOOK_URL=https://spectrum.omsflow.com/api/po_webhook.php

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Port for Cloud Run handler (local development)
PORT=8080

# Enable debug mode for Flask (development only)
FLASK_DEBUG=true

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# For production deployment, ensure these are set:
# - FASTAPI_BASE_URL should point to your deployed FastAPI service
# - SERVICE_TOKEN should be a secure, randomly generated token
# - ENVIRONMENT should be set to "production"
# - LOG_LEVEL should be "INFO" or "WARNING"
# - ALLOW_UNAUTHENTICATED should be "false"
# - Remove or set FLASK_DEBUG to "false"

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit actual credentials to version control
# 2. Use Google Cloud Secret Manager for production credentials
# 3. Rotate service tokens regularly
# 4. Use IAM roles and service accounts for authentication
# 5. Enable audit logging for production environments
