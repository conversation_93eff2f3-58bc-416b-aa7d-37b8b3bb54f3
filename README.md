# Gmail Push Notification System

A real-time email processing system that integrates Gmail API push notifications with Google Cloud Pub/Sub and Cloud Run to reduce email processing latency from 5 minutes to 30 seconds.

## 🚀 Overview

This system replaces schedule-based email processing with event-driven processing using Gmail push notifications. When new emails arrive, Gmail sends notifications to a Pub/Sub topic, which triggers a Cloud Run service that processes emails in real-time using the existing Gemini AI pipeline.

### Architecture

```
Gmail API → Pub/Sub Topic → Cloud Run Handler → FastAPI Backend → Gemini AI → Webhooks
```

### Key Features

- **Real-time Processing**: Emails processed within 60 seconds of arrival
- **Event-driven Architecture**: No more polling, only process when needed
- **Scalable**: Cloud Run automatically scales based on demand
- **Reliable**: Pub/Sub ensures message delivery with retry logic
- **Backward Compatible**: Existing scheduled processing remains as backup
- **Comprehensive Testing**: Full test suite with mock message generation

## 📁 Project Structure

```
├── cloud_run_handler.py          # Flask app for handling Pub/Sub notifications
├── gmail_push_setup.py           # Gmail watch management and setup
├── backend/
│   ├── main.py                   # Enhanced FastAPI with watch management endpoints
│   └── routers/
│       └── batch_email_router.py # Enhanced with single email processing
├── Dockerfile                    # Cloud Run deployment configuration
├── docker-compose.yml           # Local development setup
├── requirements-cloudrun.txt     # Cloud Run dependencies
├── deploy-cloudrun.sh           # Deployment script
├── test_*.py                    # Comprehensive test suite
├── mock_pubsub_messages.py      # Test message generator
├── run_tests.py                 # Test runner
├── SETUP_GUIDE.md              # Detailed setup instructions
├── DEPLOYMENT_CHECKLIST.md     # Deployment verification
└── .env.example                # Environment configuration template
```

## 🏗️ Components

### 1. Cloud Run Handler (`cloud_run_handler.py`)
- **Purpose**: Receives Gmail push notifications from Pub/Sub
- **Framework**: Flask (lightweight for Pub/Sub handling)
- **Port**: 8080 (Cloud Run standard)
- **Key Functions**:
  - Parse base64-encoded Pub/Sub messages
  - Find users by email address in Firestore
  - Fetch new emails using Gmail API
  - Trigger FastAPI email processing

### 2. Gmail Push Setup (`gmail_push_setup.py`)
- **Purpose**: Manages Gmail API watch requests
- **Features**:
  - Set up watches for user mailboxes
  - Handle watch expiration and renewal (7-day limit)
  - Batch setup for all existing users
  - Health monitoring and status reporting

### 3. Enhanced FastAPI Backend
- **New Endpoint**: `POST /emails/process-single`
- **Watch Management**:
  - `POST /gmail/setup-watches` - Initialize watches
  - `POST /gmail/renew-watches` - Renew expiring watches
  - `GET /gmail/watch-status` - Monitor watch health
  - `GET /monitoring/email-stats` - Performance metrics

### 4. Testing Framework
- **Unit Tests**: Cloud Run handler and Gmail integration
- **Integration Tests**: End-to-end email processing pipeline
- **Mock Services**: Local development without cloud infrastructure
- **Performance Tests**: Latency and throughput validation

## 🚀 Quick Start

### Prerequisites
- Google Cloud Project with billing enabled
- Firebase project
- Gmail API credentials
- Gemini AI API key

### Local Development

1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd email-analyzer
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Start services**:
   ```bash
   docker-compose up
   ```

3. **Run tests**:
   ```bash
   python run_tests.py
   ```

4. **Send test notifications**:
   ```bash
   python mock_pubsub_messages.py --mode test
   ```

### Production Deployment

1. **Deploy Cloud Run handler**:
   ```bash
   chmod +x deploy-cloudrun.sh
   ./deploy-cloudrun.sh
   ```

2. **Initialize Gmail watches**:
   ```bash
   curl -X POST "YOUR_FASTAPI_URL/gmail/setup-watches" \
     -H "Authorization: Bearer YOUR_USER_TOKEN"
   ```

3. **Verify deployment**:
   ```bash
   curl https://your-cloud-run-url/health
   ```

## 📊 Performance

### Target Metrics
- **Processing Latency**: < 60 seconds (vs 5 minutes previously)
- **System Availability**: > 99.9%
- **Error Rate**: < 1%
- **Webhook Delivery**: > 95% success rate

### Monitoring
- Cloud Run metrics (requests, latency, errors)
- Pub/Sub metrics (delivery rate, backlog)
- Gmail watch health (active, expired, renewal rate)
- Email processing stats (by source, category, timing)

## 🔧 Configuration

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `FASTAPI_BASE_URL` | FastAPI service URL | `https://your-api.com` |
| `GOOGLE_CLOUD_PROJECT` | GCP project ID | `ai-email-bot-455814` |
| `PUBSUB_TOPIC` | Pub/Sub topic name | `projects/.../topics/email-notifications` |
| `SERVICE_TOKEN` | Auth token for services | `secure-random-token` |
| `GEMINI_API_KEY` | Gemini AI API key | `your-api-key` |

### Gmail Watch Configuration
- **Topic**: `projects/ai-email-bot-455814/topics/email-notifications`
- **Labels**: INBOX only
- **Expiration**: 7 days (Gmail maximum)
- **Renewal**: Automated daily check

## 🧪 Testing

### Test Types
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Service-to-service communication
3. **End-to-End Tests**: Complete email processing pipeline
4. **Load Tests**: Performance under high volume
5. **Mock Tests**: Local development without external services

### Running Tests
```bash
# Run all tests
python run_tests.py

# Run specific test types
python test_cloud_run_handler.py
python test_gmail_integration.py
python test_email_pipeline.py

# Send mock notifications
python mock_pubsub_messages.py --mode sequence --count 5
```

## 🔒 Security

### Authentication
- Service-to-service authentication with secure tokens
- OAuth 2.0 for Gmail API access
- Firebase authentication for user requests
- IAM roles with least privilege principle

### Data Protection
- HTTPS enforced for all communications
- Sensitive data encrypted at rest in Firestore
- No credentials stored in logs
- Audit logging for all operations

## 📚 Documentation

- **[Setup Guide](SETUP_GUIDE.md)**: Detailed installation and configuration
- **[Deployment Checklist](DEPLOYMENT_CHECKLIST.md)**: Production deployment verification
- **[Backend README](backend/README.md)**: FastAPI backend documentation

## 🔄 Maintenance

### Automated Tasks
- **Watch Renewal**: Automatically renews Gmail watches before expiration
- **Credential Refresh**: OAuth tokens refresh automatically
- **Health Monitoring**: Continuous monitoring with alerts

### Manual Tasks
- Monitor error rates and performance metrics
- Review and rotate service tokens
- Update dependencies and security patches
- Capacity planning and scaling adjustments

## 🆘 Troubleshooting

### Common Issues

1. **No notifications received**:
   - Check Gmail watch status: `GET /gmail/watch-status`
   - Verify Pub/Sub subscription configuration
   - Review Cloud Run logs

2. **Processing delays**:
   - Monitor Cloud Run scaling metrics
   - Check FastAPI response times
   - Verify Gemini AI quota usage

3. **Authentication errors**:
   - Verify service account permissions
   - Check OAuth token validity
   - Review IAM role assignments

### Support
- Check logs in Google Cloud Console
- Review troubleshooting documentation
- Test with mock messages locally
- Verify environment configuration

## 📈 Roadmap

### Planned Enhancements
- [ ] Multi-region deployment for global availability
- [ ] Advanced filtering for email categories
- [ ] Real-time analytics dashboard
- [ ] Machine learning for email prioritization
- [ ] Integration with additional email providers

### Performance Optimizations
- [ ] Intelligent batching for high-volume users
- [ ] Caching layer for frequently accessed data
- [ ] Predictive scaling based on usage patterns
- [ ] Advanced retry logic with exponential backoff

---

## 📄 License

This project is part of the Email Analyzer system. See the main project documentation for licensing information.

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation for changes
4. Verify all tests pass before submitting
5. Use the deployment checklist for production changes
