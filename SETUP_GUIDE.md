# Gmail Push Notification System Setup Guide

This guide walks you through setting up the real-time email processing system that integrates Gmail API push notifications with Google Cloud Pub/Sub and Cloud Run.

## 🏗️ Architecture Overview

```
Gmail API → Pub/Sub Topic → Cloud Run Handler → FastAPI Backend → Gemini AI → Webhooks
```

1. **Gmail API** sends push notifications when new emails arrive
2. **Google Cloud Pub/Sub** receives and queues notifications
3. **Cloud Run Handler** processes notifications and finds new emails
4. **FastAPI Backend** analyzes emails using existing Gemini AI pipeline
5. **Webhooks** are triggered for purchase orders and invoices

## 📋 Prerequisites

### Required Services
- Google Cloud Project with billing enabled
- Firebase project (can be the same as Google Cloud)
- Gmail API credentials
- Gemini AI API key

### Required APIs
Enable these APIs in your Google Cloud Console:
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable gmail.googleapis.com
```

## 🔧 Setup Steps

### 1. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your actual values:
   - `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID
   - `GEMINI_API_KEY`: Your Gemini AI API key
   - `FASTAPI_BASE_URL`: URL of your FastAPI service
   - `SERVICE_TOKEN`: Secure token for service-to-service auth

### 2. Google Cloud Setup

1. **Create Pub/Sub Topic**:
   ```bash
   gcloud pubsub topics create email-notifications
   ```

2. **Set up IAM permissions**:
   ```bash
   # Allow Gmail to publish to Pub/Sub
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member=serviceAccount:<EMAIL> \
     --role=roles/pubsub.publisher
   ```

3. **Create service account for Cloud Run**:
   ```bash
   gcloud iam service-accounts create gmail-push-handler \
     --display-name="Gmail Push Handler"
   
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:gmail-push-handler@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/pubsub.subscriber"
   
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:gmail-push-handler@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/datastore.user"
   ```

### 3. Deploy Cloud Run Handler

1. **Build and deploy**:
   ```bash
   chmod +x deploy-cloudrun.sh
   ./deploy-cloudrun.sh
   ```

   Or manually:
   ```bash
   # Build image
   docker build -t gcr.io/YOUR_PROJECT_ID/gmail-push-handler .
   
   # Push to registry
   docker push gcr.io/YOUR_PROJECT_ID/gmail-push-handler
   
   # Deploy to Cloud Run
   gcloud run deploy gmail-push-handler \
     --image gcr.io/YOUR_PROJECT_ID/gmail-push-handler \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

2. **Configure Pub/Sub subscription**:
   ```bash
   CLOUD_RUN_URL=$(gcloud run services describe gmail-push-handler --region=us-central1 --format='value(status.url)')
   
   gcloud pubsub subscriptions create gmail-notifications-subscription \
     --topic=email-notifications \
     --push-endpoint="${CLOUD_RUN_URL}/gmail-webhook"
   ```

### 4. FastAPI Backend Updates

1. **Update FastAPI service** with the new endpoints (already implemented):
   - `/emails/process-single` - Process individual emails
   - `/gmail/setup-watches` - Initialize Gmail watches
   - `/gmail/renew-watches` - Renew expiring watches
   - `/gmail/watch-status` - Monitor watch health

2. **Deploy updated FastAPI** to your hosting platform

### 5. Initialize Gmail Watches

1. **Set up watches for existing users**:
   ```bash
   # Via FastAPI endpoint
   curl -X POST "YOUR_FASTAPI_URL/gmail/setup-watches" \
     -H "Authorization: Bearer YOUR_USER_TOKEN"
   ```

2. **Or use the command-line tool**:
   ```bash
   python gmail_push_setup.py setup-all
   ```

## 🧪 Testing

### Local Development

1. **Start services locally**:
   ```bash
   # Start with Docker Compose
   docker-compose up
   
   # Or start individually
   python cloud_run_handler.py  # Port 8080
   cd backend && python main.py  # Port 8000
   ```

2. **Run tests**:
   ```bash
   python run_tests.py
   ```

3. **Send test notifications**:
   ```bash
   # Send a test notification
   python mock_pubsub_messages.py --mode test --email <EMAIL>
   
   # Send multiple test messages
   python mock_pubsub_messages.py --mode sequence --count 5
   ```

### Production Testing

1. **Health checks**:
   ```bash
   curl https://your-cloud-run-url/health
   curl https://your-fastapi-url/
   ```

2. **Test Gmail watch setup**:
   ```bash
   curl -X POST "https://your-fastapi-url/gmail/setup-watches" \
     -H "Authorization: Bearer YOUR_USER_TOKEN"
   ```

3. **Monitor watch status**:
   ```bash
   curl "https://your-fastapi-url/gmail/watch-status" \
     -H "Authorization: Bearer YOUR_USER_TOKEN"
   ```

## 📊 Monitoring

### Key Metrics to Monitor

1. **Cloud Run Metrics**:
   - Request count and latency
   - Error rate
   - Memory and CPU usage

2. **Pub/Sub Metrics**:
   - Message delivery rate
   - Undelivered messages
   - Subscription backlog

3. **Gmail Watch Health**:
   - Active watches count
   - Expired watches
   - Watch renewal success rate

4. **Email Processing**:
   - Processing latency (target: <60 seconds)
   - Analysis success rate
   - Webhook delivery rate

### Logging

- **Cloud Run**: Logs available in Google Cloud Console
- **FastAPI**: Application logs with request tracing
- **Firestore**: Audit logs for data operations

## 🔄 Maintenance

### Regular Tasks

1. **Watch Renewal** (automated):
   - Watches expire after 7 days
   - Automatic renewal runs daily
   - Monitor renewal success rate

2. **Credential Refresh**:
   - OAuth tokens refresh automatically
   - Monitor for refresh failures

3. **Error Monitoring**:
   - Set up alerts for high error rates
   - Monitor Pub/Sub dead letter queues

### Troubleshooting

1. **No notifications received**:
   - Check Gmail watch status
   - Verify Pub/Sub subscription
   - Check Cloud Run logs

2. **Processing delays**:
   - Monitor Cloud Run scaling
   - Check FastAPI response times
   - Verify Gemini AI quota

3. **Authentication errors**:
   - Check service account permissions
   - Verify OAuth token validity
   - Review IAM roles

## 🔒 Security Considerations

1. **Service Authentication**:
   - Use secure service tokens
   - Rotate tokens regularly
   - Implement request signing

2. **Data Protection**:
   - Encrypt sensitive data at rest
   - Use HTTPS for all communications
   - Implement audit logging

3. **Access Control**:
   - Principle of least privilege
   - Regular access reviews
   - Monitor for unusual activity

## 📈 Performance Optimization

1. **Scaling Configuration**:
   - Cloud Run: Max 10 instances
   - Concurrency: 100 requests per instance
   - Timeout: 300 seconds

2. **Caching Strategy**:
   - Cache email data in Firestore
   - Avoid duplicate processing
   - Cache Gmail API responses

3. **Rate Limiting**:
   - Gmail API: 1 billion quota units/day
   - Gemini AI: Monitor usage quotas
   - Implement exponential backoff

## 🆘 Support

For issues and questions:
1. Check the logs in Google Cloud Console
2. Review the troubleshooting section
3. Test with mock messages locally
4. Verify all environment variables are set correctly
