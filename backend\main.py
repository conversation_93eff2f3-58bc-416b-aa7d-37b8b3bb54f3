import os
import logging
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import firebase_admin
from firebase_admin import credentials, firestore
from dotenv import load_dotenv
import uvicorn
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("main")

# Try to import email_scheduler and background_processor, but don't fail if they're not available
try:
    import email_scheduler
    scheduler_available = True
except ImportError as e:
    logger.warning(f"Email scheduler not available: {str(e)}. Scheduled email retrieval disabled.")
    scheduler_available = False

try:
    import background_processor
    background_processor_available = True
    logger.info("Background processor module loaded successfully")
except ImportError as e:
    logger.warning(f"Background processor not available: {str(e)}. Automated email processing disabled.")
    background_processor_available = False

# Import routers
from routers import gmail_router, gemini_router, auth_router, batch_email_router

# Import cloud trigger system
try:
    import cloud_email_trigger
    cloud_trigger_available = True
    logger.info("Cloud email trigger system loaded successfully")
except ImportError as e:
    logger.warning(f"Cloud email trigger system not available: {str(e)}")
    cloud_trigger_available = False

# Load environment variables
load_dotenv()

# Set development environment variables
os.environ["ALLOW_UNAUTHENTICATED"] = "true"  # Allow unauthenticated requests in development

# Set Gmail credentials JSON path if available
gmail_credentials_path = os.getenv("GMAIL_CREDENTIALS_JSON_PATH")
if gmail_credentials_path and os.path.exists(gmail_credentials_path):
    gmail_router.CREDENTIALS_FILE_PATH = gmail_credentials_path

# Initialize FastAPI app
app = FastAPI(
    title="Email Analyzer API",
    description="API for analyzing emails and extracting data from attachments using Gemini AI",
    version="1.0.0"
)

# Add custom exception handler for HTTPException
@app.exception_handler(HTTPException)
def http_exception_handler(request: Request, exc: HTTPException):
    logger.warning(f"HTTPException handler called with status_code={exc.status_code}, detail='{exc.detail}'")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers=exc.headers
    )

# Add CORS middleware with improved configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://ai-email-bot-455814.web.app"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["Authorization", "Content-Type", "Accept", "Origin", "User-Agent", "DNT", "Cache-Control", "X-Mx-ReqToken", "Keep-Alive", "X-Requested-With", "If-Modified-Since"],
    expose_headers=["Content-Length", "Content-Range"],
    max_age=1200,  # Cache preflight requests for 20 minutes
)

# Initialize Firebase
cred_path = os.getenv("FIREBASE_CREDENTIALS_PATH")

try:
    if not cred_path:
        raise ValueError("FIREBASE_CREDENTIALS_PATH environment variable not set")

    if not os.path.exists(cred_path):
        raise ValueError(f"Firebase credentials file not found at: {cred_path}")

    cred = credentials.Certificate(cred_path)

    # Initialize Firebase with storage bucket
    storage_bucket = os.getenv("FIREBASE_STORAGE_BUCKET")
    if storage_bucket:
        firebase_admin.initialize_app(cred, {
            'storageBucket': storage_bucket
        })
    else:
        firebase_admin.initialize_app(cred)

    logger.info("Firebase initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Firebase: {str(e)}")

# Initialize the background processor and email scheduler with enhanced error handling and recovery
if background_processor_available:
    try:
        # Log startup attempt to Firestore
        db = firestore.client()
        system_status_ref = db.collection('system_status').document('background_processor')
        system_status_ref.set({
            'status': 'starting',
            'startup_attempt': firestore.SERVER_TIMESTAMP,
            'environment': os.getenv('ENV', 'development')
        })

        # Start the background processor with the default interval (5 minutes)
        background_processor.start_background_processor()

        # Update status in Firestore
        system_status_ref.update({
            'status': 'running',
            'started_at': firestore.SERVER_TIMESTAMP,
            'last_error': None,
            'process_interval': background_processor.CURRENT_PROCESS_INTERVAL
        })

        logger.info(f"Background processor started successfully with {background_processor.CURRENT_PROCESS_INTERVAL/60}-minute interval")
    except Exception as e:
        logger.error(f"Failed to start background processor: {str(e)}")
        # Log error to Firestore for monitoring
        try:
            db = firestore.client()
            system_status_ref = db.collection('system_status').document('background_processor')
            system_status_ref.set({
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'error_time': firestore.SERVER_TIMESTAMP
            }, merge=True)

            # Also log to system_errors collection
            system_error_ref = db.collection('system_errors').document()
            system_error_ref.set({
                'component': 'background_processor_startup',
                'error': str(e),
                'error_type': type(e).__name__,
                'timestamp': firestore.SERVER_TIMESTAMP
            })
        except Exception as log_err:
            logger.critical(f"Failed to log background processor startup error: {str(log_err)}")

if scheduler_available:
    try:
        # Log startup attempt to Firestore
        db = firestore.client()
        system_status_ref = db.collection('system_status').document('email_scheduler')
        system_status_ref.set({
            'status': 'starting',
            'startup_attempt': firestore.SERVER_TIMESTAMP
        })

        # Initialize the scheduler
        email_scheduler.init_scheduler()

        # Update status in Firestore
        system_status_ref.update({
            'status': 'running',
            'started_at': firestore.SERVER_TIMESTAMP,
            'last_error': None
        })

        logger.info("Email scheduler initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize email scheduler: {str(e)}")
        # Log error to Firestore for monitoring
        try:
            db = firestore.client()
            system_status_ref = db.collection('system_status').document('email_scheduler')
            system_status_ref.set({
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'error_time': firestore.SERVER_TIMESTAMP
            }, merge=True)

            # Also log to system_errors collection
            system_error_ref = db.collection('system_errors').document()
            system_error_ref.set({
                'component': 'email_scheduler_startup',
                'error': str(e),
                'error_type': type(e).__name__,
                'timestamp': firestore.SERVER_TIMESTAMP
            })
        except Exception as log_err:
            logger.critical(f"Failed to log email scheduler startup error: {str(log_err)}")

# Include routers
app.include_router(auth_router.router, prefix="/auth", tags=["Authentication"])
app.include_router(gmail_router.router, prefix="/gmail", tags=["Gmail"])
app.include_router(gemini_router.router, prefix="/gemini", tags=["Gemini AI"])
app.include_router(batch_email_router.router, prefix="/emails", tags=["Email Batch Operations"])

# Include cloud trigger router if available
if cloud_trigger_available:
    app.include_router(cloud_email_trigger.router, prefix="/cloud", tags=["Cloud Email Triggers"])
    logger.info("Cloud email trigger endpoints registered")

# Start automatic 10-minute email processing scheduler (HIGHEST PRIORITY)
async def start_auto_email_processing():
    """Start the automatic email processing scheduler on app startup"""
    if cloud_trigger_available:
        try:
            logger.info("🚀 Starting automatic 10-minute email processing scheduler...")
            logger.info("📋 Processing ALL emails (new and existing) with force_reanalysis=True")
            await cloud_email_trigger.start_auto_scheduler()
            logger.info("✅ Automatic email processing scheduler started successfully")
            
            # Log success to Firestore
            try:
                db = firestore.client()
                startup_log_ref = db.collection('system_startup').document()
                startup_log_ref.set({
                    'component': 'auto_email_scheduler',
                    'status': 'started',
                    'timestamp': firestore.SERVER_TIMESTAMP,
                    'interval_seconds': 600,  # 10 minutes
                    'force_reanalysis': True,  # Analyze all emails
                    'environment': os.getenv('ENV', 'production')
                })
            except Exception as log_err:
                logger.warning(f"Failed to log auto-scheduler startup: {str(log_err)}")
                
        except Exception as e:
            logger.error(f"❌ Failed to start automatic email processing scheduler: {str(e)}")
            # Log error to Firestore
            try:
                db = firestore.client()
                startup_error_ref = db.collection('system_errors').document()
                startup_error_ref.set({
                    'component': 'auto_email_scheduler_startup',
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'timestamp': firestore.SERVER_TIMESTAMP
                })
            except Exception as log_err:
                logger.critical(f"Failed to log auto-scheduler startup error: {str(log_err)}")

# Schedule the auto-scheduler to start after app initialization
@app.on_event("startup")
async def startup_event():
    """FastAPI startup event to initialize automatic email processing"""
    logger.info("🔧 FastAPI startup event triggered")
    await start_auto_email_processing()

@app.on_event("shutdown")
async def shutdown_event():
    """FastAPI shutdown event to cleanup automatic email processing"""
    logger.info("🛑 FastAPI shutdown event triggered")
    if cloud_trigger_available:
        try:
            logger.info("Stopping automatic email processing scheduler...")
            await cloud_email_trigger.stop_auto_scheduler()
            logger.info("✅ Automatic email processing scheduler stopped")
        except Exception as e:
            logger.error(f"❌ Failed to stop automatic email processing scheduler: {str(e)}")

@app.get("/")
async def root():
    return {"message": "Welcome to Email Analyzer API"}

@app.get("/health")
async def health_check():
    """Enhanced health check with cloud trigger functionality"""
    try:
        # Basic health status
        health_status = {
            "status": "healthy", 
            "message": "Email Analyzer API is running",
            "timestamp": datetime.now().isoformat()
        }
        
        # Add cloud trigger health check if available
        if cloud_trigger_available:
            try:
                cloud_health = await cloud_email_trigger.enhanced_health_check()
                health_status["cloud_trigger"] = cloud_health
                
                # Optionally trigger processing via health check
                # This provides a fallback mechanism for cloud deployments
                if cloud_health.get("trigger_needed"):
                    logger.info("Health check detected need for email processing trigger")
                    # You can uncomment the next line to auto-trigger via health checks
                    # await cloud_email_trigger.process_emails_cloud_safe()
                    
            except Exception as e:
                health_status["cloud_trigger"] = {
                    "status": "error",
                    "error": str(e)
                }
                logger.error(f"Cloud trigger health check failed: {str(e)}")
        
        # Add background processor status
        if background_processor_available:
            try:
                bg_status = background_processor.get_processor_status()
                health_status["background_processor"] = bg_status
            except Exception as e:
                health_status["background_processor"] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # Add scheduler status  
        if scheduler_available:
            try:
                scheduler_status = email_scheduler.get_scheduler_status()
                health_status["scheduler"] = scheduler_status
            except Exception as e:
                health_status["scheduler"] = {
                    "status": "error", 
                    "error": str(e)
                }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# Add an endpoint to check the background processor status
@app.get("/background-status")
async def background_status():
    """Get the status of the background processor"""
    if background_processor_available:
        return background_processor.get_processor_status()
    else:
        return {
            "status": "inactive",
            "message": "Background processor is not available"
        }

# Add an endpoint to update the background processor interval
class IntervalUpdate(BaseModel):
    interval_seconds: int

@app.post("/background-interval")
async def update_background_interval(interval: IntervalUpdate):
    """Update the background processor interval"""
    if background_processor_available:
        success = background_processor.set_process_interval(interval.interval_seconds)
        if success:
            return {
                "success": True,
                "message": f"Background processor interval updated to {interval.interval_seconds} seconds",
                "new_interval": background_processor.CURRENT_PROCESS_INTERVAL
            }
        else:
            return {
                "success": False,
                "message": "Failed to update background processor interval"
            }
    else:
        return {
            "success": False,
            "message": "Background processor is not available"
        }

# Add an endpoint to reset the email counters
@app.post("/reset-email-counters")
async def reset_email_counters():
    """Reset the email counters to zero"""
    if background_processor_available:
        success = background_processor.reset_counters()
        if success:
            return {
                "success": True,
                "message": "Email counters reset to zero"
            }
        else:
            return {
                "success": False,
                "message": "Failed to reset email counters"
            }
    else:
        return {
            "success": False,
            "message": "Background processor is not available"
        }

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
