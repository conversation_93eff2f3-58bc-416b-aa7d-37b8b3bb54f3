import os
import logging
import async<PERSON>
from datetime import datetime
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import firebase_admin
from firebase_admin import credentials, firestore
from dotenv import load_dotenv
import uvicorn
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("main")

# Try to import email_scheduler and background_processor, but don't fail if they're not available
try:
    import email_scheduler
    scheduler_available = True
except ImportError as e:
    logger.warning(f"Email scheduler not available: {str(e)}. Scheduled email retrieval disabled.")
    scheduler_available = False

try:
    import background_processor
    background_processor_available = True
    logger.info("Background processor module loaded successfully")
except ImportError as e:
    logger.warning(f"Background processor not available: {str(e)}. Automated email processing disabled.")
    background_processor_available = False

# Import routers
from routers import gmail_router, gemini_router, auth_router, batch_email_router

# Import cloud trigger system
try:
    import cloud_email_trigger
    cloud_trigger_available = True
    logger.info("Cloud email trigger system loaded successfully")
except ImportError as e:
    logger.warning(f"Cloud email trigger system not available: {str(e)}")
    cloud_trigger_available = False

# Load environment variables
load_dotenv()

# Set development environment variables
os.environ["ALLOW_UNAUTHENTICATED"] = "true"  # Allow unauthenticated requests in development

# Set Gmail credentials JSON path if available
gmail_credentials_path = os.getenv("GMAIL_CREDENTIALS_JSON_PATH")
if gmail_credentials_path and os.path.exists(gmail_credentials_path):
    gmail_router.CREDENTIALS_FILE_PATH = gmail_credentials_path

# Initialize FastAPI app
app = FastAPI(
    title="Email Analyzer API",
    description="API for analyzing emails and extracting data from attachments using Gemini AI",
    version="1.0.0"
)

# Add custom exception handler for HTTPException
@app.exception_handler(HTTPException)
def http_exception_handler(request: Request, exc: HTTPException):
    logger.warning(f"HTTPException handler called with status_code={exc.status_code}, detail='{exc.detail}'")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers=exc.headers
    )

# Add CORS middleware with improved configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://ai-email-bot-455814.web.app"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["Authorization", "Content-Type", "Accept", "Origin", "User-Agent", "DNT", "Cache-Control", "X-Mx-ReqToken", "Keep-Alive", "X-Requested-With", "If-Modified-Since"],
    expose_headers=["Content-Length", "Content-Range"],
    max_age=1200,  # Cache preflight requests for 20 minutes
)

# Initialize Firebase
cred_path = os.getenv("FIREBASE_CREDENTIALS_PATH")

try:
    if not cred_path:
        raise ValueError("FIREBASE_CREDENTIALS_PATH environment variable not set")

    if not os.path.exists(cred_path):
        raise ValueError(f"Firebase credentials file not found at: {cred_path}")

    cred = credentials.Certificate(cred_path)

    # Initialize Firebase with storage bucket
    storage_bucket = os.getenv("FIREBASE_STORAGE_BUCKET")
    if storage_bucket:
        firebase_admin.initialize_app(cred, {
            'storageBucket': storage_bucket
        })
    else:
        firebase_admin.initialize_app(cred)

    logger.info("Firebase initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Firebase: {str(e)}")

# Initialize the background processor and email scheduler with enhanced error handling and recovery
if background_processor_available:
    try:
        # Log startup attempt to Firestore
        db = firestore.client()
        system_status_ref = db.collection('system_status').document('background_processor')
        system_status_ref.set({
            'status': 'starting',
            'startup_attempt': firestore.SERVER_TIMESTAMP,
            'environment': os.getenv('ENV', 'development')
        })

        # Start the background processor with the default interval (5 minutes)
        background_processor.start_background_processor()

        # Update status in Firestore
        system_status_ref.update({
            'status': 'running',
            'started_at': firestore.SERVER_TIMESTAMP,
            'last_error': None,
            'process_interval': background_processor.CURRENT_PROCESS_INTERVAL
        })

        logger.info(f"Background processor started successfully with {background_processor.CURRENT_PROCESS_INTERVAL/60}-minute interval")
    except Exception as e:
        logger.error(f"Failed to start background processor: {str(e)}")
        # Log error to Firestore for monitoring
        try:
            db = firestore.client()
            system_status_ref = db.collection('system_status').document('background_processor')
            system_status_ref.set({
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'error_time': firestore.SERVER_TIMESTAMP
            }, merge=True)

            # Also log to system_errors collection
            system_error_ref = db.collection('system_errors').document()
            system_error_ref.set({
                'component': 'background_processor_startup',
                'error': str(e),
                'error_type': type(e).__name__,
                'timestamp': firestore.SERVER_TIMESTAMP
            })
        except Exception as log_err:
            logger.critical(f"Failed to log background processor startup error: {str(log_err)}")

if scheduler_available:
    try:
        # Log startup attempt to Firestore
        db = firestore.client()
        system_status_ref = db.collection('system_status').document('email_scheduler')
        system_status_ref.set({
            'status': 'starting',
            'startup_attempt': firestore.SERVER_TIMESTAMP
        })

        # Initialize the scheduler
        email_scheduler.init_scheduler()

        # Update status in Firestore
        system_status_ref.update({
            'status': 'running',
            'started_at': firestore.SERVER_TIMESTAMP,
            'last_error': None
        })

        logger.info("Email scheduler initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize email scheduler: {str(e)}")
        # Log error to Firestore for monitoring
        try:
            db = firestore.client()
            system_status_ref = db.collection('system_status').document('email_scheduler')
            system_status_ref.set({
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'error_time': firestore.SERVER_TIMESTAMP
            }, merge=True)

            # Also log to system_errors collection
            system_error_ref = db.collection('system_errors').document()
            system_error_ref.set({
                'component': 'email_scheduler_startup',
                'error': str(e),
                'error_type': type(e).__name__,
                'timestamp': firestore.SERVER_TIMESTAMP
            })
        except Exception as log_err:
            logger.critical(f"Failed to log email scheduler startup error: {str(log_err)}")

# Import Gmail push notification manager
try:
    import gmail_push_setup
    gmail_push_available = True
    logger.info("Gmail push notification manager loaded successfully")
except ImportError as e:
    logger.warning(f"Gmail push notification manager not available: {str(e)}")
    gmail_push_available = False

# Include routers
app.include_router(auth_router.router, prefix="/auth", tags=["Authentication"])
app.include_router(gmail_router.router, prefix="/gmail", tags=["Gmail"])
app.include_router(gemini_router.router, prefix="/gemini", tags=["Gemini AI"])
app.include_router(batch_email_router.router, prefix="/emails", tags=["Email Batch Operations"])

# Include cloud trigger router if available
if cloud_trigger_available:
    app.include_router(cloud_email_trigger.router, prefix="/cloud", tags=["Cloud Email Triggers"])
    logger.info("Cloud email trigger endpoints registered")

# Start automatic 10-minute email processing scheduler (HIGHEST PRIORITY)
async def start_auto_email_processing():
    """Start the automatic email processing scheduler on app startup"""
    if cloud_trigger_available:
        try:
            logger.info("🚀 Starting automatic 10-minute email processing scheduler...")
            logger.info("📋 Processing ALL emails (new and existing) with force_reanalysis=True")
            await cloud_email_trigger.start_auto_scheduler()
            logger.info("✅ Automatic email processing scheduler started successfully")
            
            # Log success to Firestore
            try:
                db = firestore.client()
                startup_log_ref = db.collection('system_startup').document()
                startup_log_ref.set({
                    'component': 'auto_email_scheduler',
                    'status': 'started',
                    'timestamp': firestore.SERVER_TIMESTAMP,
                    'interval_seconds': 600,  # 10 minutes
                    'force_reanalysis': True,  # Analyze all emails
                    'environment': os.getenv('ENV', 'production')
                })
            except Exception as log_err:
                logger.warning(f"Failed to log auto-scheduler startup: {str(log_err)}")
                
        except Exception as e:
            logger.error(f"❌ Failed to start automatic email processing scheduler: {str(e)}")
            # Log error to Firestore
            try:
                db = firestore.client()
                startup_error_ref = db.collection('system_errors').document()
                startup_error_ref.set({
                    'component': 'auto_email_scheduler_startup',
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'timestamp': firestore.SERVER_TIMESTAMP
                })
            except Exception as log_err:
                logger.critical(f"Failed to log auto-scheduler startup error: {str(log_err)}")

# Schedule the auto-scheduler to start after app initialization
@app.on_event("startup")
async def startup_event():
    """FastAPI startup event to initialize automatic email processing and Gmail watches"""
    logger.info("🔧 FastAPI startup event triggered")
    await start_auto_email_processing()

    # Initialize Gmail push notifications if available
    if gmail_push_available:
        try:
            logger.info("🔔 Initializing Gmail push notifications...")

            # Initialize watch manager
            watch_manager = gmail_push_setup.GmailWatchManager()

            # Set up watches for all users (in background to avoid blocking startup)
            import asyncio
            asyncio.create_task(setup_gmail_watches_on_startup(watch_manager))

            logger.info("✅ Gmail push notification initialization started")

        except Exception as e:
            logger.error(f"❌ Failed to initialize Gmail push notifications: {str(e)}")
            # Log error to Firestore
            try:
                db = firestore.client()
                startup_error_ref = db.collection('system_errors').document()
                startup_error_ref.set({
                    'component': 'gmail_push_startup',
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'timestamp': firestore.SERVER_TIMESTAMP
                })
            except Exception as log_err:
                logger.critical(f"Failed to log Gmail push startup error: {str(log_err)}")

async def setup_gmail_watches_on_startup(watch_manager):
    """Background task to set up Gmail watches for all users on startup"""
    try:
        logger.info("Setting up Gmail watches for all users on startup...")

        # Small delay to ensure Firestore is ready
        await asyncio.sleep(5)

        result = watch_manager.setup_all_user_watches()

        if result['success']:
            logger.info(f"✅ Gmail watch startup complete: {result['setup_count']} setup, {result['failed_count']} failed, {result['skipped_count']} skipped")
        else:
            logger.error(f"❌ Gmail watch startup failed: {result.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Error setting up Gmail watches on startup: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """FastAPI shutdown event to cleanup automatic email processing"""
    logger.info("🛑 FastAPI shutdown event triggered")
    if cloud_trigger_available:
        try:
            logger.info("Stopping automatic email processing scheduler...")
            await cloud_email_trigger.stop_auto_scheduler()
            logger.info("✅ Automatic email processing scheduler stopped")
        except Exception as e:
            logger.error(f"❌ Failed to stop automatic email processing scheduler: {str(e)}")

@app.get("/")
async def root():
    return {"message": "Welcome to Email Analyzer API"}

# Gmail Watch Management Endpoints
@app.post("/gmail/setup-watches")
async def setup_gmail_watches(user_data: dict = Depends(auth_router.verify_token)):
    """Initialize Gmail watches for all user accounts"""
    try:
        if not gmail_push_available:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gmail push notification service not available"
            )

        logger.info(f"Setting up Gmail watches for user {user_data['uid']}")

        # Initialize watch manager
        watch_manager = gmail_push_setup.GmailWatchManager()

        # Get user's email accounts
        db = firestore.client()
        user_id = user_data['uid']

        accounts_ref = db.collection('users').document(user_id).collection('email_accounts')
        accounts = accounts_ref.stream()

        setup_results = []

        for account_doc in accounts:
            account_id = account_doc.id
            account_data = account_doc.to_dict()
            credentials_dict = account_data.get('credentials', {})

            if not credentials_dict:
                setup_results.append({
                    'account_id': account_id,
                    'success': False,
                    'error': 'No credentials found'
                })
                continue

            # Set up watch for this account
            watch_result = watch_manager.setup_watch_for_user(user_id, account_id, credentials_dict)

            setup_results.append({
                'account_id': account_id,
                'success': watch_result is not None,
                'watch_data': watch_result if watch_result else None,
                'error': None if watch_result else 'Failed to setup watch'
            })

        successful_setups = sum(1 for result in setup_results if result['success'])

        return {
            "success": True,
            "message": f"Gmail watch setup completed for user {user_id}",
            "total_accounts": len(setup_results),
            "successful_setups": successful_setups,
            "results": setup_results
        }

    except Exception as e:
        logger.error(f"Error setting up Gmail watches: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error setting up Gmail watches: {str(e)}"
        )

@app.post("/gmail/renew-watches")
async def renew_gmail_watches(user_data: dict = Depends(auth_router.verify_token)):
    """Renew expiring Gmail watches for user"""
    try:
        if not gmail_push_available:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gmail push notification service not available"
            )

        logger.info(f"Renewing Gmail watches for user {user_data['uid']}")

        # Initialize watch manager
        watch_manager = gmail_push_setup.GmailWatchManager()

        # Get user's expiring watches
        user_id = user_data['uid']
        db = firestore.client()

        # Get watches expiring in the next 24 hours for this user
        from datetime import datetime, timedelta
        expiry_threshold = datetime.now() + timedelta(hours=24)
        expiry_timestamp = expiry_threshold.timestamp()

        watches_ref = db.collection('users').document(user_id).collection('gmail_watches')
        expiring_watches = watches_ref.where('expiration', '<=', expiry_timestamp).where('status', '==', 'active').stream()

        renewal_results = []

        for watch_doc in expiring_watches:
            account_id = watch_doc.id

            success = watch_manager.renew_watch(user_id, account_id)
            renewal_results.append({
                'account_id': account_id,
                'success': success,
                'error': None if success else 'Failed to renew watch'
            })

        successful_renewals = sum(1 for result in renewal_results if result['success'])

        return {
            "success": True,
            "message": f"Gmail watch renewal completed for user {user_id}",
            "total_watches": len(renewal_results),
            "successful_renewals": successful_renewals,
            "results": renewal_results
        }

    except Exception as e:
        logger.error(f"Error renewing Gmail watches: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error renewing Gmail watches: {str(e)}"
        )

@app.get("/gmail/watch-status")
async def get_gmail_watch_status(user_data: dict = Depends(auth_router.verify_token)):
    """Get Gmail watch status for user"""
    try:
        if not gmail_push_available:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Gmail push notification service not available"
            )

        logger.info(f"Getting Gmail watch status for user {user_data['uid']}")

        user_id = user_data['uid']
        db = firestore.client()

        # Get user's watches
        watches_ref = db.collection('users').document(user_id).collection('gmail_watches')
        watches = watches_ref.stream()

        watch_status = []
        current_time = datetime.now().timestamp()

        for watch_doc in watches:
            watch_data = watch_doc.to_dict()
            account_id = watch_doc.id

            expiration = watch_data.get('expiration', 0)
            is_active = watch_data.get('status') == 'active'
            is_expired = expiration <= current_time

            # Calculate time until expiration
            time_until_expiry = None
            if not is_expired:
                time_until_expiry = expiration - current_time

            watch_status.append({
                'account_id': account_id,
                'email_address': watch_data.get('email_address'),
                'status': watch_data.get('status'),
                'is_active': is_active,
                'is_expired': is_expired,
                'expiration': expiration,
                'expiration_datetime': watch_data.get('expiration_datetime'),
                'time_until_expiry_seconds': time_until_expiry,
                'created_at': watch_data.get('created_at'),
                'history_id': watch_data.get('historyId')
            })

        active_watches = sum(1 for watch in watch_status if watch['is_active'] and not watch['is_expired'])
        expired_watches = sum(1 for watch in watch_status if watch['is_expired'])

        return {
            "success": True,
            "user_id": user_id,
            "total_watches": len(watch_status),
            "active_watches": active_watches,
            "expired_watches": expired_watches,
            "watches": watch_status
        }

    except Exception as e:
        logger.error(f"Error getting Gmail watch status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting Gmail watch status: {str(e)}"
        )

@app.get("/monitoring/email-stats")
async def get_email_processing_stats(user_data: dict = Depends(auth_router.verify_token)):
    """Get email processing performance metrics"""
    try:
        user_id = user_data['uid']
        db = firestore.client()

        # Get recent email analyses (last 24 hours)
        from datetime import datetime, timedelta
        yesterday = datetime.now() - timedelta(days=1)

        analyses_ref = db.collection('users').document(user_id).collection('email_analyses')
        recent_analyses = analyses_ref.where('processed_at', '>=', yesterday).stream()

        stats = {
            'total_processed_24h': 0,
            'by_source': {},
            'by_category': {},
            'processing_times': [],
            'webhook_triggered': 0,
            'errors': 0
        }

        for analysis_doc in recent_analyses:
            analysis_data = analysis_doc.to_dict()

            stats['total_processed_24h'] += 1

            # Count by source
            source = analysis_data.get('source', 'unknown')
            stats['by_source'][source] = stats['by_source'].get(source, 0) + 1

            # Count by category
            category = analysis_data.get('category', 'unknown')
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1

            # Check for errors
            if analysis_data.get('error', False):
                stats['errors'] += 1

            # Check for webhook categories
            webhook_categories = ['purchase_order', 'invoice', 'order_confirmation', 'order']
            if category.lower() in webhook_categories:
                stats['webhook_triggered'] += 1

        # Get Gmail watch status
        watch_stats = {
            'total_watches': 0,
            'active_watches': 0,
            'expired_watches': 0
        }

        if gmail_push_available:
            try:
                watches_ref = db.collection('users').document(user_id).collection('gmail_watches')
                watches = watches_ref.stream()

                current_time = datetime.now().timestamp()

                for watch_doc in watches:
                    watch_data = watch_doc.to_dict()
                    watch_stats['total_watches'] += 1

                    expiration = watch_data.get('expiration', 0)
                    is_active = watch_data.get('status') == 'active'
                    is_expired = expiration <= current_time

                    if is_active and not is_expired:
                        watch_stats['active_watches'] += 1
                    elif is_expired:
                        watch_stats['expired_watches'] += 1

            except Exception as watch_error:
                logger.warning(f"Error getting watch stats: {str(watch_error)}")

        return {
            "success": True,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "email_processing": stats,
            "gmail_watches": watch_stats,
            "real_time_enabled": gmail_push_available
        }

    except Exception as e:
        logger.error(f"Error getting email processing stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting email processing stats: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """Enhanced health check with cloud trigger functionality"""
    try:
        # Basic health status
        health_status = {
            "status": "healthy", 
            "message": "Email Analyzer API is running",
            "timestamp": datetime.now().isoformat()
        }
        
        # Add cloud trigger health check if available
        if cloud_trigger_available:
            try:
                cloud_health = await cloud_email_trigger.enhanced_health_check()
                health_status["cloud_trigger"] = cloud_health
                
                # Optionally trigger processing via health check
                # This provides a fallback mechanism for cloud deployments
                if cloud_health.get("trigger_needed"):
                    logger.info("Health check detected need for email processing trigger")
                    # You can uncomment the next line to auto-trigger via health checks
                    # await cloud_email_trigger.process_emails_cloud_safe()
                    
            except Exception as e:
                health_status["cloud_trigger"] = {
                    "status": "error",
                    "error": str(e)
                }
                logger.error(f"Cloud trigger health check failed: {str(e)}")
        
        # Add background processor status
        if background_processor_available:
            try:
                bg_status = background_processor.get_processor_status()
                health_status["background_processor"] = bg_status
            except Exception as e:
                health_status["background_processor"] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # Add scheduler status  
        if scheduler_available:
            try:
                scheduler_status = email_scheduler.get_scheduler_status()
                health_status["scheduler"] = scheduler_status
            except Exception as e:
                health_status["scheduler"] = {
                    "status": "error", 
                    "error": str(e)
                }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# Add an endpoint to check the background processor status
@app.get("/background-status")
async def background_status():
    """Get the status of the background processor"""
    if background_processor_available:
        return background_processor.get_processor_status()
    else:
        return {
            "status": "inactive",
            "message": "Background processor is not available"
        }

# Add an endpoint to update the background processor interval
class IntervalUpdate(BaseModel):
    interval_seconds: int

@app.post("/background-interval")
async def update_background_interval(interval: IntervalUpdate):
    """Update the background processor interval"""
    if background_processor_available:
        success = background_processor.set_process_interval(interval.interval_seconds)
        if success:
            return {
                "success": True,
                "message": f"Background processor interval updated to {interval.interval_seconds} seconds",
                "new_interval": background_processor.CURRENT_PROCESS_INTERVAL
            }
        else:
            return {
                "success": False,
                "message": "Failed to update background processor interval"
            }
    else:
        return {
            "success": False,
            "message": "Background processor is not available"
        }

# Add an endpoint to reset the email counters
@app.post("/reset-email-counters")
async def reset_email_counters():
    """Reset the email counters to zero"""
    if background_processor_available:
        success = background_processor.reset_counters()
        if success:
            return {
                "success": True,
                "message": "Email counters reset to zero"
            }
        else:
            return {
                "success": False,
                "message": "Failed to reset email counters"
            }
    else:
        return {
            "success": False,
            "message": "Background processor is not available"
        }

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
