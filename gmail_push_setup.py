#!/usr/bin/env python3
"""
Gmail Push Notification Manager

This module handles setting up, managing, and renewing Gmail API watch requests
for real-time email notifications via Google Cloud Pub/Sub.

Features:
- Set up Gmail API watch requests for user mailboxes
- Configure watch to send notifications to Pub/Sub topic
- Store watch metadata in Firestore
- Handle watch expiration and renewal (Gmail watches expire after 7 days)
- Batch setup function for all existing users
- Monitor watch health and provide status reporting
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

from google.cloud import firestore
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request as GoogleRequest
from googleapiclient.errors import HttpError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("gmail_push_setup")

# Configuration
GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-email-bot-455814')
PUBSUB_TOPIC = os.getenv('PUBSUB_TOPIC', 'projects/ai-email-bot-455814/topics/email-notifications')
WATCH_EXPIRATION_DAYS = 7  # Gmail maximum

class GmailWatchManager:
    """Manages Gmail API watch requests for push notifications"""

    def __init__(self):
        self.db = firestore.Client()
        self.project_id = GOOGLE_CLOUD_PROJECT
        self.topic_name = PUBSUB_TOPIC

    def refresh_credentials_if_needed(self, credentials_dict: Dict[str, Any]) -> Optional[Credentials]:
        """Refresh Gmail API credentials if needed"""
        try:
            credentials = Credentials(
                token=credentials_dict.get('token'),
                refresh_token=credentials_dict.get('refresh_token'),
                token_uri=credentials_dict.get('token_uri'),
                client_id=credentials_dict.get('client_id'),
                client_secret=credentials_dict.get('client_secret'),
                scopes=credentials_dict.get('scopes', [])
            )

            # Check if credentials need refresh
            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    logger.info("Refreshing expired credentials")
                    credentials.refresh(GoogleRequest())
                    return credentials
                else:
                    logger.error("Credentials are invalid and cannot be refreshed")
                    return None

            return credentials

        except Exception as e:
            logger.error(f"Error refreshing credentials: {str(e)}")
            return None

    def setup_watch_for_user(self, user_id: str, account_id: str, credentials_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create Gmail watch for a specific user account"""
        try:
            logger.info(f"Setting up Gmail watch for user {user_id}, account {account_id}")

            # Refresh credentials if needed
            credentials = self.refresh_credentials_if_needed(credentials_dict)
            if not credentials:
                logger.error(f"Failed to get valid credentials for user {user_id}, account {account_id}")
                return None

            # Build Gmail service
            service = build('gmail', 'v1', credentials=credentials)

            # Get user profile to verify access
            try:
                profile = service.users().getProfile(userId='me').execute()
                email_address = profile.get('emailAddress')
                logger.info(f"Setting up watch for email: {email_address}")
            except Exception as profile_error:
                logger.error(f"Failed to get user profile: {str(profile_error)}")
                return None

            # Stop any existing watch first
            try:
                service.users().stop(userId='me').execute()
                logger.info(f"Stopped existing watch for {email_address}")
            except HttpError as e:
                if e.resp.status != 404:  # 404 means no existing watch
                    logger.warning(f"Error stopping existing watch: {str(e)}")

            # Set up new watch request
            watch_request = {
                'topicName': self.topic_name,
                'labelIds': ['INBOX'],  # Only watch INBOX
                'labelFilterAction': 'include'
            }

            # Create the watch
            watch_response = service.users().watch(userId='me', body=watch_request).execute()

            logger.info(f"Successfully created Gmail watch: {watch_response}")

            # Calculate expiration time (Gmail watches expire after 7 days max)
            expiration_timestamp = int(watch_response.get('expiration', 0)) / 1000  # Convert from milliseconds
            expiration_datetime = datetime.fromtimestamp(expiration_timestamp)

            # Prepare watch metadata
            watch_metadata = {
                'historyId': watch_response.get('historyId'),
                'expiration': expiration_timestamp,
                'expiration_datetime': expiration_datetime.isoformat(),
                'email_address': email_address,
                'topic_name': self.topic_name,
                'label_ids': ['INBOX'],
                'created_at': firestore.SERVER_TIMESTAMP,
                'status': 'active',
                'user_id': user_id,
                'account_id': account_id
            }

            # Store watch details in Firestore
            self.store_watch_details(user_id, account_id, watch_metadata)

            return watch_metadata

        except Exception as e:
            logger.error(f"Error setting up Gmail watch for user {user_id}: {str(e)}")
            return None

    def store_watch_details(self, user_id: str, account_id: str, watch_metadata: Dict[str, Any]) -> bool:
        """Store watch metadata in Firestore"""
        try:
            # Store in users/{userId}/gmail_watches/{accountId}
            watch_ref = self.db.collection('users').document(user_id).collection('gmail_watches').document(account_id)
            watch_ref.set(watch_metadata)

            logger.info(f"Stored watch details for user {user_id}, account {account_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing watch details: {str(e)}")
            return False

    def get_expiring_watches(self, hours_before_expiry: int = 24) -> List[Dict[str, Any]]:
        """Get watches that will expire within the specified hours"""
        try:
            expiry_threshold = datetime.now() + timedelta(hours=hours_before_expiry)
            expiry_timestamp = expiry_threshold.timestamp()

            logger.info(f"Looking for watches expiring before {expiry_threshold.isoformat()}")

            expiring_watches = []

            # Query all users
            users_ref = self.db.collection('users')
            users = users_ref.stream()

            for user_doc in users:
                user_id = user_doc.id

                # Check gmail_watches subcollection
                watches_ref = users_ref.document(user_id).collection('gmail_watches')
                watches = watches_ref.where('expiration', '<=', expiry_timestamp).where('status', '==', 'active').stream()

                for watch_doc in watches:
                    watch_data = watch_doc.to_dict()
                    watch_data['user_id'] = user_id
                    watch_data['account_id'] = watch_doc.id
                    expiring_watches.append(watch_data)

            logger.info(f"Found {len(expiring_watches)} expiring watches")
            return expiring_watches

        except Exception as e:
            logger.error(f"Error getting expiring watches: {str(e)}")
            return []

    def renew_watch(self, user_id: str, account_id: str) -> bool:
        """Renew a specific Gmail watch"""
        try:
            logger.info(f"Renewing watch for user {user_id}, account {account_id}")

            # Get user's email account credentials
            account_ref = self.db.collection('users').document(user_id).collection('email_accounts').document(account_id)
            account_doc = account_ref.get()

            if not account_doc.exists:
                logger.error(f"Email account {account_id} not found for user {user_id}")
                return False

            account_data = account_doc.to_dict()
            credentials_dict = account_data.get('credentials', {})

            if not credentials_dict:
                logger.error(f"No credentials found for account {account_id}")
                return False

            # Set up new watch (this will replace the old one)
            watch_metadata = self.setup_watch_for_user(user_id, account_id, credentials_dict)

            if watch_metadata:
                logger.info(f"Successfully renewed watch for user {user_id}, account {account_id}")
                return True
            else:
                logger.error(f"Failed to renew watch for user {user_id}, account {account_id}")
                return False

        except Exception as e:
            logger.error(f"Error renewing watch: {str(e)}")
            return False

    def renew_expired_watches(self) -> Dict[str, Any]:
        """Renew all watches that are expiring soon"""
        try:
            logger.info("Starting renewal of expired watches")

            # Get watches expiring in the next 24 hours
            expiring_watches = self.get_expiring_watches(24)

            if not expiring_watches:
                logger.info("No watches need renewal")
                return {
                    'success': True,
                    'message': 'No watches need renewal',
                    'renewed_count': 0,
                    'failed_count': 0
                }

            renewed_count = 0
            failed_count = 0

            for watch in expiring_watches:
                user_id = watch['user_id']
                account_id = watch['account_id']

                success = self.renew_watch(user_id, account_id)
                if success:
                    renewed_count += 1
                else:
                    failed_count += 1

            logger.info(f"Watch renewal complete. Renewed: {renewed_count}, Failed: {failed_count}")

            return {
                'success': True,
                'message': f'Processed {len(expiring_watches)} watches',
                'renewed_count': renewed_count,
                'failed_count': failed_count,
                'total_processed': len(expiring_watches)
            }

        except Exception as e:
            logger.error(f"Error renewing expired watches: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def setup_all_user_watches(self) -> Dict[str, Any]:
        """Initialize Gmail watches for all users with email accounts"""
        try:
            logger.info("Setting up Gmail watches for all users")

            setup_count = 0
            failed_count = 0
            skipped_count = 0

            # Query all users
            users_ref = self.db.collection('users')
            users = users_ref.stream()

            for user_doc in users:
                user_id = user_doc.id
                logger.info(f"Processing user {user_id}")

                # Get user's email accounts
                email_accounts_ref = users_ref.document(user_id).collection('email_accounts')
                accounts = email_accounts_ref.stream()

                for account_doc in accounts:
                    account_id = account_doc.id
                    account_data = account_doc.to_dict()

                    # Check if watch already exists and is active
                    watch_ref = users_ref.document(user_id).collection('gmail_watches').document(account_id)
                    existing_watch = watch_ref.get()

                    if existing_watch.exists:
                        watch_data = existing_watch.to_dict()
                        if watch_data.get('status') == 'active':
                            # Check if watch is still valid (not expired)
                            expiration = watch_data.get('expiration', 0)
                            if expiration > datetime.now().timestamp():
                                logger.info(f"Active watch already exists for user {user_id}, account {account_id}")
                                skipped_count += 1
                                continue

                    # Set up new watch
                    credentials_dict = account_data.get('credentials', {})
                    if not credentials_dict:
                        logger.warning(f"No credentials found for user {user_id}, account {account_id}")
                        failed_count += 1
                        continue

                    watch_metadata = self.setup_watch_for_user(user_id, account_id, credentials_dict)

                    if watch_metadata:
                        setup_count += 1
                        logger.info(f"Successfully set up watch for user {user_id}, account {account_id}")
                    else:
                        failed_count += 1
                        logger.error(f"Failed to set up watch for user {user_id}, account {account_id}")

            logger.info(f"Watch setup complete. Setup: {setup_count}, Failed: {failed_count}, Skipped: {skipped_count}")

            return {
                'success': True,
                'message': f'Processed all user watches',
                'setup_count': setup_count,
                'failed_count': failed_count,
                'skipped_count': skipped_count,
                'total_processed': setup_count + failed_count + skipped_count
            }

        except Exception as e:
            logger.error(f"Error setting up all user watches: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_watch_status(self) -> Dict[str, Any]:
        """Get status of all Gmail watches"""
        try:
            logger.info("Getting status of all Gmail watches")

            active_watches = []
            expired_watches = []
            total_users = 0

            current_time = datetime.now().timestamp()

            # Query all users
            users_ref = self.db.collection('users')
            users = users_ref.stream()

            for user_doc in users:
                user_id = user_doc.id
                total_users += 1

                # Check gmail_watches subcollection
                watches_ref = users_ref.document(user_id).collection('gmail_watches')
                watches = watches_ref.stream()

                for watch_doc in watches:
                    watch_data = watch_doc.to_dict()
                    watch_data['user_id'] = user_id
                    watch_data['account_id'] = watch_doc.id

                    expiration = watch_data.get('expiration', 0)
                    if expiration > current_time and watch_data.get('status') == 'active':
                        active_watches.append(watch_data)
                    else:
                        expired_watches.append(watch_data)

            return {
                'success': True,
                'total_users': total_users,
                'active_watches': len(active_watches),
                'expired_watches': len(expired_watches),
                'watch_details': {
                    'active': active_watches,
                    'expired': expired_watches
                }
            }

        except Exception as e:
            logger.error(f"Error getting watch status: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def stop_watch_for_user(self, user_id: str, account_id: str) -> bool:
        """Stop Gmail watch for a specific user account"""
        try:
            logger.info(f"Stopping Gmail watch for user {user_id}, account {account_id}")

            # Get user's email account credentials
            account_ref = self.db.collection('users').document(user_id).collection('email_accounts').document(account_id)
            account_doc = account_ref.get()

            if not account_doc.exists:
                logger.error(f"Email account {account_id} not found for user {user_id}")
                return False

            account_data = account_doc.to_dict()
            credentials_dict = account_data.get('credentials', {})

            if not credentials_dict:
                logger.error(f"No credentials found for account {account_id}")
                return False

            # Refresh credentials if needed
            credentials = self.refresh_credentials_if_needed(credentials_dict)
            if not credentials:
                logger.error(f"Failed to get valid credentials for user {user_id}, account {account_id}")
                return False

            # Build Gmail service
            service = build('gmail', 'v1', credentials=credentials)

            # Stop the watch
            try:
                service.users().stop(userId='me').execute()
                logger.info(f"Successfully stopped Gmail watch for user {user_id}, account {account_id}")

                # Update watch status in Firestore
                watch_ref = self.db.collection('users').document(user_id).collection('gmail_watches').document(account_id)
                watch_ref.update({
                    'status': 'stopped',
                    'stopped_at': firestore.SERVER_TIMESTAMP
                })

                return True

            except HttpError as e:
                if e.resp.status == 404:
                    logger.info(f"No active watch found for user {user_id}, account {account_id}")
                    return True
                else:
                    logger.error(f"Error stopping watch: {str(e)}")
                    return False

        except Exception as e:
            logger.error(f"Error stopping Gmail watch: {str(e)}")
            return False

# Main execution functions
def main():
    """Main function for command-line usage"""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python gmail_push_setup.py <command>")
        print("Commands:")
        print("  setup-all    - Set up watches for all users")
        print("  renew        - Renew expiring watches")
        print("  status       - Get watch status")
        print("  setup-user <user_id> <account_id> - Set up watch for specific user")
        return

    command = sys.argv[1]
    manager = GmailWatchManager()

    if command == "setup-all":
        result = manager.setup_all_user_watches()
        print(f"Setup result: {result}")

    elif command == "renew":
        result = manager.renew_expired_watches()
        print(f"Renewal result: {result}")

    elif command == "status":
        result = manager.get_watch_status()
        print(f"Status result: {result}")

    elif command == "setup-user" and len(sys.argv) >= 4:
        user_id = sys.argv[2]
        account_id = sys.argv[3]

        # Get credentials from Firestore
        try:
            db = firestore.Client()
            account_ref = db.collection('users').document(user_id).collection('email_accounts').document(account_id)
            account_doc = account_ref.get()

            if not account_doc.exists:
                print(f"Account {account_id} not found for user {user_id}")
                return

            account_data = account_doc.to_dict()
            credentials_dict = account_data.get('credentials', {})

            if not credentials_dict:
                print(f"No credentials found for account {account_id}")
                return

            result = manager.setup_watch_for_user(user_id, account_id, credentials_dict)
            print(f"Setup result: {result}")

        except Exception as e:
            print(f"Error: {str(e)}")

    else:
        print("Invalid command or missing arguments")

if __name__ == "__main__":
    main()