#!/usr/bin/env python3
"""
Mock Pub/Sub Message Generator

Generates test Pub/Sub messages for Gmail push notifications to test
the Cloud Run handler locally without actual Gmail API integration.
"""

import json
import base64
import time
import random
from datetime import datetime
from typing import Dict, Any, List
import requests

class MockPubSubMessageGenerator:
    """Generates mock Pub/Sub messages for testing"""
    
    def __init__(self, cloud_run_url: str = "http://localhost:8080"):
        self.cloud_run_url = cloud_run_url
        self.webhook_endpoint = f"{cloud_run_url}/gmail-webhook"
        self.test_endpoint = f"{cloud_run_url}/test-notification"
        
        # Sample email addresses for testing
        self.test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>"
        ]
    
    def generate_gmail_notification(self, email_address: str = None, history_id: str = None) -> Dict[str, Any]:
        """Generate a Gmail push notification payload"""
        if not email_address:
            email_address = random.choice(self.test_emails)
        
        if not history_id:
            history_id = str(random.randint(10000, 99999))
        
        notification_data = {
            "emailAddress": email_address,
            "historyId": history_id
        }
        
        return notification_data
    
    def create_pubsub_message(self, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Pub/Sub message format"""
        # Encode notification data as base64
        json_data = json.dumps(notification_data)
        encoded_data = base64.b64encode(json_data.encode()).decode()
        
        pubsub_message = {
            "message": {
                "data": encoded_data,
                "messageId": f"test-message-{int(time.time())}-{random.randint(1000, 9999)}",
                "publishTime": datetime.now().isoformat() + "Z",
                "attributes": {
                    "source": "mock-test"
                }
            },
            "subscription": "projects/ai-email-bot-455814/subscriptions/gmail-notifications-subscription"
        }
        
        return pubsub_message
    
    def send_test_notification(self, email_address: str = None, history_id: str = None) -> Dict[str, Any]:
        """Send a test notification to the Cloud Run handler"""
        try:
            # Generate notification
            notification_data = self.generate_gmail_notification(email_address, history_id)
            
            print(f"📧 Sending test notification for {notification_data['emailAddress']}")
            print(f"📊 History ID: {notification_data['historyId']}")
            
            # Send to test endpoint (development mode)
            response = requests.post(
                self.test_endpoint,
                json=notification_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Test notification sent successfully")
                print(f"📋 Response: {json.dumps(result, indent=2)}")
                return result
            else:
                print(f"❌ Test notification failed: {response.status_code}")
                print(f"📋 Response: {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            print(f"❌ Error sending test notification: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def send_pubsub_message(self, email_address: str = None, history_id: str = None) -> Dict[str, Any]:
        """Send a Pub/Sub formatted message to the webhook endpoint"""
        try:
            # Generate notification and Pub/Sub message
            notification_data = self.generate_gmail_notification(email_address, history_id)
            pubsub_message = self.create_pubsub_message(notification_data)
            
            print(f"📧 Sending Pub/Sub message for {notification_data['emailAddress']}")
            print(f"📊 History ID: {notification_data['historyId']}")
            print(f"📨 Message ID: {pubsub_message['message']['messageId']}")
            
            # Send to webhook endpoint
            response = requests.post(
                self.webhook_endpoint,
                json=pubsub_message,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Pub/Sub message sent successfully")
                print(f"📋 Response: {json.dumps(result, indent=2)}")
                return result
            else:
                print(f"❌ Pub/Sub message failed: {response.status_code}")
                print(f"📋 Response: {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            print(f"❌ Error sending Pub/Sub message: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_health_check(self) -> bool:
        """Test the health check endpoint"""
        try:
            print("🏥 Testing health check endpoint...")
            response = requests.get(f"{self.cloud_run_url}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Health check passed")
                print(f"📋 Status: {health_data.get('status', 'unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")
            return False
    
    def run_test_sequence(self, num_messages: int = 3) -> List[Dict[str, Any]]:
        """Run a sequence of test messages"""
        print(f"🚀 Starting test sequence with {num_messages} messages")
        
        # First, test health check
        if not self.test_health_check():
            print("❌ Health check failed, aborting test sequence")
            return []
        
        results = []
        
        for i in range(num_messages):
            print(f"\n📨 Test message {i+1}/{num_messages}")
            
            # Alternate between test endpoint and webhook endpoint
            if i % 2 == 0:
                result = self.send_test_notification()
            else:
                result = self.send_pubsub_message()
            
            results.append(result)
            
            # Small delay between messages
            if i < num_messages - 1:
                print("⏳ Waiting 2 seconds before next message...")
                time.sleep(2)
        
        print(f"\n🏁 Test sequence completed")
        successful = sum(1 for r in results if r.get('success', False))
        print(f"📊 Results: {successful}/{num_messages} successful")
        
        return results

def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Mock Pub/Sub Message Generator for Gmail Push Notifications')
    parser.add_argument('--url', default='http://localhost:8080', help='Cloud Run handler URL')
    parser.add_argument('--email', help='Email address for notification')
    parser.add_argument('--history-id', help='History ID for notification')
    parser.add_argument('--count', type=int, default=1, help='Number of test messages to send')
    parser.add_argument('--mode', choices=['test', 'pubsub', 'sequence'], default='test', 
                       help='Test mode: test (test endpoint), pubsub (webhook), sequence (multiple)')
    
    args = parser.parse_args()
    
    generator = MockPubSubMessageGenerator(args.url)
    
    if args.mode == 'sequence':
        generator.run_test_sequence(args.count)
    elif args.mode == 'pubsub':
        for i in range(args.count):
            print(f"\n📨 Sending Pub/Sub message {i+1}/{args.count}")
            generator.send_pubsub_message(args.email, args.history_id)
            if i < args.count - 1:
                time.sleep(1)
    else:  # test mode
        for i in range(args.count):
            print(f"\n📨 Sending test notification {i+1}/{args.count}")
            generator.send_test_notification(args.email, args.history_id)
            if i < args.count - 1:
                time.sleep(1)

if __name__ == "__main__":
    main()
