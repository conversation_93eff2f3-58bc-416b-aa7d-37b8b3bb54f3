#!/usr/bin/env python3
"""
Cloud Run Handler for Gmail Push Notifications

This service handles Gmail push notifications from Google Cloud Pub/Sub,
processes them, and triggers email analysis via the FastAPI backend.

Architecture:
1. Receives POST requests from Pub/Sub on /gmail-webhook
2. Parses base64-encoded messages containing Gmail notifications
3. Extracts emailAddress and historyId from notifications
4. Queries Firestore to find user by email address
5. Uses Gmail API to fetch new emails since historyId
6. Makes HTTP POST calls to FastAPI /emails/process-single endpoint
"""

import os
import json
import base64
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List

from flask import Flask, request, jsonify
import requests
from google.cloud import firestore
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request as GoogleRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("cloud_run_handler")

# Initialize Flask app
app = Flask(__name__)

# Configuration from environment variables
FASTAPI_BASE_URL = os.getenv('FASTAPI_BASE_URL', 'http://localhost:8000')
SERVICE_TOKEN = os.getenv('SERVICE_TOKEN', 'dev-service-token')
GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-email-bot-455814')
PUBSUB_TOPIC = os.getenv('PUBSUB_TOPIC', 'projects/ai-email-bot-455814/topics/email-notifications')
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

# Set log level
logger.setLevel(getattr(logging, LOG_LEVEL.upper()))

# Initialize Firestore client
try:
    db = firestore.Client()
    logger.info("Firestore client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Firestore client: {str(e)}")
    db = None

class GmailNotificationProcessor:
    """Handles Gmail push notification processing"""

    def __init__(self):
        self.db = db
        self.fastapi_base_url = FASTAPI_BASE_URL
        self.service_token = SERVICE_TOKEN

    def parse_pubsub_message(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse Pub/Sub message and extract Gmail notification data"""
        try:
            # Extract message from Pub/Sub format
            message = request_data.get('message', {})
            if not message:
                logger.error("No message found in Pub/Sub request")
                return None

            # Decode base64 data
            data = message.get('data', '')
            if not data:
                logger.error("No data found in Pub/Sub message")
                return None

            # Decode base64 message
            try:
                decoded_data = base64.b64decode(data).decode('utf-8')
                notification_data = json.loads(decoded_data)
                logger.info(f"Decoded notification data: {notification_data}")
                return notification_data
            except Exception as decode_error:
                logger.error(f"Failed to decode Pub/Sub message: {str(decode_error)}")
                return None

        except Exception as e:
            logger.error(f"Error parsing Pub/Sub message: {str(e)}")
            return None

    def get_user_by_email(self, email_address: str) -> Optional[Dict[str, Any]]:
        """Find user in Firestore by email address in email_accounts collection"""
        try:
            if not self.db:
                logger.error("Firestore client not available")
                return None

            logger.info(f"Searching for user with email: {email_address}")

            # Query all users and their email accounts
            users_ref = self.db.collection('users')
            users = users_ref.stream()

            for user_doc in users:
                user_id = user_doc.id
                user_data = user_doc.to_dict()

                # Check email_accounts subcollection
                email_accounts_ref = users_ref.document(user_id).collection('email_accounts')
                accounts = email_accounts_ref.where('email', '==', email_address).stream()

                for account_doc in accounts:
                    account_data = account_doc.to_dict()
                    logger.info(f"Found user {user_id} with email account {account_doc.id}")

                    return {
                        'user_id': user_id,
                        'user_data': user_data,
                        'account_id': account_doc.id,
                        'account_data': account_data,
                        'email_address': email_address
                    }

            logger.warning(f"No user found with email address: {email_address}")
            return None

        except Exception as e:
            logger.error(f"Error finding user by email: {str(e)}")
            return None

    def refresh_credentials_if_needed(self, credentials_dict: Dict[str, Any]) -> Optional[Credentials]:
        """Refresh Gmail API credentials if needed"""
        try:
            credentials = Credentials(
                token=credentials_dict.get('token'),
                refresh_token=credentials_dict.get('refresh_token'),
                token_uri=credentials_dict.get('token_uri'),
                client_id=credentials_dict.get('client_id'),
                client_secret=credentials_dict.get('client_secret'),
                scopes=credentials_dict.get('scopes', [])
            )

            # Check if credentials need refresh
            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    logger.info("Refreshing expired credentials")
                    credentials.refresh(GoogleRequest())
                    return credentials
                else:
                    logger.error("Credentials are invalid and cannot be refreshed")
                    return None

            return credentials

        except Exception as e:
            logger.error(f"Error refreshing credentials: {str(e)}")
            return None

    def fetch_new_emails(self, user_data: Dict[str, Any], history_id: str) -> List[Dict[str, Any]]:
        """Fetch new emails from Gmail API since the given history ID"""
        try:
            user_id = user_data['user_id']
            account_id = user_data['account_id']
            account_data = user_data['account_data']

            # Get credentials from account data
            credentials_dict = account_data.get('credentials', {})
            if not credentials_dict:
                logger.error(f"No credentials found for account {account_id}")
                return []

            # Refresh credentials if needed
            credentials = self.refresh_credentials_if_needed(credentials_dict)
            if not credentials:
                logger.error(f"Failed to get valid credentials for account {account_id}")
                return []

            # Build Gmail service
            service = build('gmail', 'v1', credentials=credentials)

            # Get history since the given history ID
            logger.info(f"Fetching history since {history_id} for user {user_id}")

            try:
                history_response = service.users().history().list(
                    userId='me',
                    startHistoryId=history_id,
                    historyTypes=['messageAdded'],
                    labelId='INBOX'
                ).execute()

                history_records = history_response.get('history', [])
                logger.info(f"Found {len(history_records)} history records")

                new_emails = []
                for record in history_records:
                    messages_added = record.get('messagesAdded', [])
                    for message_added in messages_added:
                        message = message_added.get('message', {})
                        email_id = message.get('id')

                        if email_id:
                            # Check if message is in INBOX
                            label_ids = message.get('labelIds', [])
                            if 'INBOX' in label_ids:
                                new_emails.append({
                                    'email_id': email_id,
                                    'user_id': user_id,
                                    'account_id': account_id,
                                    'thread_id': message.get('threadId'),
                                    'label_ids': label_ids
                                })
                                logger.info(f"Found new email: {email_id}")

                logger.info(f"Total new emails found: {len(new_emails)}")
                return new_emails

            except Exception as history_error:
                logger.error(f"Error fetching Gmail history: {str(history_error)}")
                # If history fails, we can't determine new emails
                return []

        except Exception as e:
            logger.error(f"Error fetching new emails: {str(e)}")
            return []

    def trigger_email_analysis(self, email_id: str, user_id: str, account_id: str) -> bool:
        """Trigger email analysis via FastAPI endpoint"""
        try:
            # Prepare request payload
            payload = {
                'email_id': email_id,
                'user_id': user_id,
                'account_id': account_id,
                'priority': 'real_time',
                'source': 'gmail_push_notification',
                'force_reanalysis': False
            }

            # Prepare headers with service token
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.service_token}',
                'X-Service-Source': 'cloud-run-handler'
            }

            # Make request to FastAPI
            url = f"{self.fastapi_base_url}/emails/process-single"
            logger.info(f"Triggering email analysis: {url}")
            logger.info(f"Payload: {payload}")

            response = requests.post(
                url,
                json=payload,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Successfully triggered analysis for email {email_id}")
                return True
            else:
                logger.error(f"Failed to trigger analysis. Status: {response.status_code}, Response: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error triggering email analysis: {str(e)}")
            return False

    def process_gmail_notification(self, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process individual Gmail notification"""
        try:
            # Extract email address and history ID from notification
            email_address = notification_data.get('emailAddress')
            history_id = notification_data.get('historyId')

            if not email_address or not history_id:
                logger.error(f"Missing required fields in notification: {notification_data}")
                return {
                    'success': False,
                    'error': 'Missing emailAddress or historyId in notification'
                }

            logger.info(f"Processing notification for {email_address}, historyId: {history_id}")

            # Find user by email address
            user_data = self.get_user_by_email(email_address)
            if not user_data:
                logger.warning(f"No user found for email address: {email_address}")
                return {
                    'success': False,
                    'error': f'No user found for email address: {email_address}'
                }

            # Fetch new emails since history ID
            new_emails = self.fetch_new_emails(user_data, history_id)

            if not new_emails:
                logger.info(f"No new emails found for {email_address}")
                return {
                    'success': True,
                    'message': 'No new emails to process',
                    'emails_processed': 0
                }

            # Trigger analysis for each new email
            successful_triggers = 0
            failed_triggers = 0

            for email_info in new_emails:
                success = self.trigger_email_analysis(
                    email_info['email_id'],
                    email_info['user_id'],
                    email_info['account_id']
                )

                if success:
                    successful_triggers += 1
                else:
                    failed_triggers += 1

            logger.info(f"Processed {len(new_emails)} emails. Success: {successful_triggers}, Failed: {failed_triggers}")

            return {
                'success': True,
                'message': f'Processed {len(new_emails)} emails',
                'emails_processed': len(new_emails),
                'successful_triggers': successful_triggers,
                'failed_triggers': failed_triggers
            }

        except Exception as e:
            logger.error(f"Error processing Gmail notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# Initialize the processor
processor = GmailNotificationProcessor()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for Cloud Run"""
    try:
        # Basic health check
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'gmail-push-handler',
            'version': '1.0.0'
        }

        # Check Firestore connection
        if db:
            try:
                # Simple Firestore test
                test_ref = db.collection('_health_check').document('test')
                test_ref.set({'timestamp': firestore.SERVER_TIMESTAMP})
                health_status['firestore'] = 'connected'
            except Exception as db_error:
                health_status['firestore'] = f'error: {str(db_error)}'
                health_status['status'] = 'degraded'
        else:
            health_status['firestore'] = 'not_initialized'
            health_status['status'] = 'degraded'

        # Check FastAPI connectivity
        try:
            response = requests.get(f"{FASTAPI_BASE_URL}/", timeout=5)
            if response.status_code == 200:
                health_status['fastapi'] = 'connected'
            else:
                health_status['fastapi'] = f'status_code: {response.status_code}'
                health_status['status'] = 'degraded'
        except Exception as api_error:
            health_status['fastapi'] = f'error: {str(api_error)}'
            health_status['status'] = 'degraded'

        status_code = 200 if health_status['status'] == 'healthy' else 503
        return jsonify(health_status), status_code

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 503

@app.route('/gmail-webhook', methods=['POST'])
def handle_gmail_notification():
    """Handle Gmail push notifications from Pub/Sub"""
    try:
        # Log the incoming request
        logger.info("Received Gmail push notification")

        # Get request data
        request_data = request.get_json()
        if not request_data:
            logger.error("No JSON data in request")
            return jsonify({'error': 'No JSON data provided'}), 400

        logger.info(f"Request data: {request_data}")

        # Parse Pub/Sub message
        notification_data = processor.parse_pubsub_message(request_data)
        if not notification_data:
            logger.error("Failed to parse Pub/Sub message")
            return jsonify({'error': 'Failed to parse Pub/Sub message'}), 400

        # Process the notification
        result = processor.process_gmail_notification(notification_data)

        if result['success']:
            logger.info(f"Successfully processed notification: {result}")
            return jsonify(result), 200
        else:
            logger.error(f"Failed to process notification: {result}")
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Error handling Gmail notification: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500

@app.route('/test-notification', methods=['POST'])
def test_notification():
    """Test endpoint for simulating Gmail notifications (development only)"""
    try:
        # Only allow in development
        if os.getenv('ENVIRONMENT', 'development') != 'development':
            return jsonify({'error': 'Test endpoint only available in development'}), 403

        # Get test data from request
        test_data = request.get_json()
        if not test_data:
            return jsonify({'error': 'No test data provided'}), 400

        # Process as if it were a real notification
        result = processor.process_gmail_notification(test_data)

        return jsonify({
            'test_mode': True,
            'result': result
        }), 200

    except Exception as e:
        logger.error(f"Error in test notification: {str(e)}")
        return jsonify({
            'error': 'Test failed',
            'message': str(e)
        }), 500

@app.route('/', methods=['GET'])
def root():
    """Root endpoint"""
    return jsonify({
        'service': 'Gmail Push Notification Handler',
        'version': '1.0.0',
        'status': 'running',
        'endpoints': {
            'health': '/health',
            'webhook': '/gmail-webhook',
            'test': '/test-notification'
        }
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'error': 'Not found',
        'message': 'The requested endpoint does not exist'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500

if __name__ == '__main__':
    # Configuration for local development
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('ENVIRONMENT', 'development') == 'development'

    logger.info(f"Starting Gmail Push Handler on port {port}")
    logger.info(f"FastAPI Base URL: {FASTAPI_BASE_URL}")
    logger.info(f"Debug mode: {debug}")

    app.run(host='0.0.0.0', port=port, debug=debug)