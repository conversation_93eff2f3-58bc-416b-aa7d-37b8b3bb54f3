# Cloud Run service configuration for Gmail Push Handler
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: gmail-push-handler
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/maxScale: "10"
        autoscaling.knative.dev/minScale: "0"
        
        # Resource allocation
        run.googleapis.com/memory: "512Mi"
        run.googleapis.com/cpu: "1"
        
        # Timeout configuration
        run.googleapis.com/timeout: "300s"
        
        # Concurrency
        run.googleapis.com/execution-environment: gen2
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - image: gcr.io/ai-email-bot-455814/gmail-push-handler:latest
        ports:
        - containerPort: 8080
          protocol: TCP
        env:
        # FastAPI backend URL - UPDATE THIS
        - name: FASTAPI_BASE_URL
          value: "https://your-fastapi-service-url"
        
        # Google Cloud configuration
        - name: GOOGLE_CLOUD_PROJECT
          value: "ai-email-bot-455814"
        
        # Pub/Sub topic
        - name: PUBSUB_TOPIC
          value: "projects/ai-email-bot-455814/topics/email-notifications"
        
        # Environment settings
        - name: ENVIRONMENT
          value: "production"
        
        # Logging level
        - name: LOG_LEVEL
          value: "INFO"
        
        # Service token for FastAPI communication
        - name: SERVICE_TOKEN
          value: "your-secure-service-token"
        
        # Health check configuration
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # Resource limits
        resources:
          limits:
            memory: "512Mi"
            cpu: "1000m"
          requests:
            memory: "256Mi"
            cpu: "100m"
        
        # Security context
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
  
  traffic:
  - percent: 100
    latestRevision: true
