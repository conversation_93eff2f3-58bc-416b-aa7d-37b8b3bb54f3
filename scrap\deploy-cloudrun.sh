#!/bin/bash

# Cloud Run Deployment Script for Gmail Push Handler
# This script builds and deploys the Gmail push notification handler to Google Cloud Run

set -e  # Exit on any error

# Configuration
PROJECT_ID="ai-email-bot-455814"
SERVICE_NAME="gmail-push-handler"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"
FASTAPI_BASE_URL="https://your-fastapi-service-url"  # Update this with your FastAPI URL

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Cloud Run deployment for Gmail Push Handler${NC}"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting Google Cloud project to ${PROJECT_ID}${NC}"
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required Google Cloud APIs${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable firestore.googleapis.com

# Build the Docker image
echo -e "${YELLOW}🏗️  Building Docker image${NC}"
docker build -t ${IMAGE_NAME} .

# Push the image to Google Container Registry
echo -e "${YELLOW}📤 Pushing image to Google Container Registry${NC}"
docker push ${IMAGE_NAME}

# Deploy to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run${NC}"
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --memory 512Mi \
    --cpu 1 \
    --concurrency 100 \
    --timeout 300 \
    --max-instances 10 \
    --set-env-vars "FASTAPI_BASE_URL=${FASTAPI_BASE_URL}" \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --set-env-vars "PUBSUB_TOPIC=projects/${PROJECT_ID}/topics/email-notifications" \
    --set-env-vars "ENVIRONMENT=production" \
    --set-env-vars "LOG_LEVEL=INFO"

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}📍 Service URL: ${SERVICE_URL}${NC}"
echo -e "${GREEN}🔗 Webhook endpoint: ${SERVICE_URL}/gmail-webhook${NC}"
echo -e "${GREEN}❤️  Health check: ${SERVICE_URL}/health${NC}"

# Configure Pub/Sub subscription (if not already exists)
echo -e "${YELLOW}🔔 Configuring Pub/Sub subscription${NC}"

SUBSCRIPTION_NAME="gmail-notifications-subscription"
TOPIC_NAME="email-notifications"

# Check if subscription exists
if ! gcloud pubsub subscriptions describe ${SUBSCRIPTION_NAME} &> /dev/null; then
    echo -e "${YELLOW}📝 Creating Pub/Sub subscription${NC}"
    gcloud pubsub subscriptions create ${SUBSCRIPTION_NAME} \
        --topic=${TOPIC_NAME} \
        --push-endpoint="${SERVICE_URL}/gmail-webhook" \
        --ack-deadline=60
else
    echo -e "${GREEN}✅ Pub/Sub subscription already exists${NC}"
    # Update the push endpoint in case the URL changed
    gcloud pubsub subscriptions modify-push-config ${SUBSCRIPTION_NAME} \
        --push-endpoint="${SERVICE_URL}/gmail-webhook"
fi

echo -e "${GREEN}🎉 Gmail Push Handler deployment complete!${NC}"
echo -e "${YELLOW}📋 Next steps:${NC}"
echo -e "   1. Update your FastAPI service URL in the environment variables if needed"
echo -e "   2. Set up Gmail API credentials and service account"
echo -e "   3. Test the webhook endpoint: ${SERVICE_URL}/gmail-webhook"
echo -e "   4. Initialize Gmail watches via your FastAPI service"
