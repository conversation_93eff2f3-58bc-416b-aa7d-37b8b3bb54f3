# Docker Compose for local development and testing
version: '3.8'

services:
  # Gmail Push Handler (Cloud Run simulation)
  gmail-push-handler:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - FASTAPI_BASE_URL=http://fastapi-backend:8000
      - GOOGLE_CLOUD_PROJECT=ai-email-bot-455814
      - PUBSUB_TOPIC=projects/ai-email-bot-455814/topics/email-notifications
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - SERVICE_TOKEN=dev-service-token
    volumes:
      # Mount credentials for local development
      - ./backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json:/app/firebase-credentials.json:ro
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/firebase-credentials.json
    depends_on:
      - fastapi-backend
    networks:
      - email-analyzer-network

  # FastAPI Backend
  fastapi-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ALLOW_UNAUTHENTICATED=true
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/firebase-credentials.json
      - GMAIL_CREDENTIALS_JSON_PATH=/app/client_secret.json
    volumes:
      - ./backend:/app
      - ./backend/ai-email-bot-455814-firebase-adminsdk-fbsvc-2308b7e9c3.json:/app/firebase-credentials.json:ro
      - ./backend/client_secret_524036921514-5o0m4i236f41nuut62daj9nrien1pgsu.apps.googleusercontent.com.json:/app/client_secret.json:ro
    networks:
      - email-analyzer-network

  # Pub/Sub Emulator for local testing
  pubsub-emulator:
    image: google/cloud-sdk:alpine
    ports:
      - "8085:8085"
    command: >
      sh -c "
        gcloud beta emulators pubsub start --host-port=0.0.0.0:8085 --project=ai-email-bot-455814
      "
    environment:
      - PUBSUB_EMULATOR_HOST=localhost:8085
    networks:
      - email-analyzer-network

  # Firestore Emulator for local testing
  firestore-emulator:
    image: google/cloud-sdk:alpine
    ports:
      - "8080:8080"
    command: >
      sh -c "
        gcloud beta emulators firestore start --host-port=0.0.0.0:8080 --project=ai-email-bot-455814
      "
    environment:
      - FIRESTORE_EMULATOR_HOST=localhost:8080
    networks:
      - email-analyzer-network

networks:
  email-analyzer-network:
    driver: bridge

# For local testing, you can override environment variables
# Create a .env file with:
# GEMINI_API_KEY=your_gemini_api_key
# FASTAPI_BASE_URL=http://localhost:8000
# GOOGLE_CLOUD_PROJECT=ai-email-bot-455814
