#!/usr/bin/env python3
"""
Gmail Push Notification System Management Tool

A comprehensive command-line tool for managing the Gmail push notification system.
Provides utilities for deployment, monitoring, testing, and maintenance.
"""

import argparse
import json
import os
import sys
import subprocess
import requests
import time
from datetime import datetime
from typing import Dict, Any, List

class GmailPushManager:
    """Management tool for Gmail push notification system"""
    
    def __init__(self):
        self.cloud_run_url = os.getenv('CLOUD_RUN_URL', 'http://localhost:8080')
        self.fastapi_url = os.getenv('FASTAPI_BASE_URL', 'http://localhost:8000')
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'ai-email-bot-455814')
        
    def print_header(self, title: str):
        """Print formatted header"""
        print("\n" + "=" * 60)
        print(f"🔧 {title}")
        print("=" * 60)
    
    def print_status(self, message: str, success: bool = True):
        """Print status message"""
        icon = "✅" if success else "❌"
        print(f"{icon} {message}")
    
    def check_health(self) -> Dict[str, bool]:
        """Check health of all services"""
        self.print_header("Health Check")
        
        results = {}
        
        # Check Cloud Run handler
        try:
            response = requests.get(f"{self.cloud_run_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                results['cloud_run'] = health_data.get('status') == 'healthy'
                self.print_status(f"Cloud Run Handler: {health_data.get('status', 'unknown')}", results['cloud_run'])
            else:
                results['cloud_run'] = False
                self.print_status(f"Cloud Run Handler: HTTP {response.status_code}", False)
        except Exception as e:
            results['cloud_run'] = False
            self.print_status(f"Cloud Run Handler: {str(e)}", False)
        
        # Check FastAPI backend
        try:
            response = requests.get(f"{self.fastapi_url}/", timeout=10)
            results['fastapi'] = response.status_code == 200
            self.print_status(f"FastAPI Backend: {'OK' if results['fastapi'] else 'Failed'}", results['fastapi'])
        except Exception as e:
            results['fastapi'] = False
            self.print_status(f"FastAPI Backend: {str(e)}", False)
        
        return results
    
    def deploy_cloud_run(self, image_tag: str = "latest") -> bool:
        """Deploy Cloud Run service"""
        self.print_header("Deploying Cloud Run Service")
        
        try:
            # Build image
            self.print_status("Building Docker image...")
            build_cmd = f"docker build -t gcr.io/{self.project_id}/gmail-push-handler:{image_tag} ."
            result = subprocess.run(build_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                self.print_status(f"Build failed: {result.stderr}", False)
                return False
            
            # Push image
            self.print_status("Pushing image to registry...")
            push_cmd = f"docker push gcr.io/{self.project_id}/gmail-push-handler:{image_tag}"
            result = subprocess.run(push_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                self.print_status(f"Push failed: {result.stderr}", False)
                return False
            
            # Deploy to Cloud Run
            self.print_status("Deploying to Cloud Run...")
            deploy_cmd = f"""
            gcloud run deploy gmail-push-handler \
                --image gcr.io/{self.project_id}/gmail-push-handler:{image_tag} \
                --platform managed \
                --region us-central1 \
                --allow-unauthenticated \
                --memory 512Mi \
                --cpu 1 \
                --concurrency 100 \
                --timeout 300 \
                --max-instances 10 \
                --set-env-vars "FASTAPI_BASE_URL={self.fastapi_url}" \
                --set-env-vars "GOOGLE_CLOUD_PROJECT={self.project_id}" \
                --set-env-vars "ENVIRONMENT=production"
            """
            
            result = subprocess.run(deploy_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.print_status("Cloud Run deployment successful", True)
                return True
            else:
                self.print_status(f"Deployment failed: {result.stderr}", False)
                return False
                
        except Exception as e:
            self.print_status(f"Deployment error: {str(e)}", False)
            return False
    
    def setup_pubsub(self) -> bool:
        """Set up Pub/Sub topic and subscription"""
        self.print_header("Setting up Pub/Sub")
        
        try:
            # Create topic
            topic_cmd = f"gcloud pubsub topics create email-notifications --project={self.project_id}"
            subprocess.run(topic_cmd, shell=True, capture_output=True)
            self.print_status("Pub/Sub topic created (or already exists)")
            
            # Get Cloud Run URL
            url_cmd = f"gcloud run services describe gmail-push-handler --region=us-central1 --format='value(status.url)' --project={self.project_id}"
            result = subprocess.run(url_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                self.print_status("Failed to get Cloud Run URL", False)
                return False
            
            cloud_run_url = result.stdout.strip()
            
            # Create subscription
            sub_cmd = f"""
            gcloud pubsub subscriptions create gmail-notifications-subscription \
                --topic=email-notifications \
                --push-endpoint="{cloud_run_url}/gmail-webhook" \
                --ack-deadline=60 \
                --project={self.project_id}
            """
            
            result = subprocess.run(sub_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0 or "already exists" in result.stderr:
                self.print_status("Pub/Sub subscription configured")
                return True
            else:
                self.print_status(f"Subscription setup failed: {result.stderr}", False)
                return False
                
        except Exception as e:
            self.print_status(f"Pub/Sub setup error: {str(e)}", False)
            return False
    
    def test_notification(self, email: str = "<EMAIL>", history_id: str = "12345") -> bool:
        """Send test notification"""
        self.print_header("Sending Test Notification")
        
        try:
            from mock_pubsub_messages import MockPubSubMessageGenerator
            
            generator = MockPubSubMessageGenerator(self.cloud_run_url)
            result = generator.send_test_notification(email, history_id)
            
            success = result.get('success', False)
            self.print_status(f"Test notification: {'Success' if success else 'Failed'}", success)
            
            if not success:
                print(f"Error: {result.get('error', 'Unknown error')}")
            
            return success
            
        except Exception as e:
            self.print_status(f"Test notification error: {str(e)}", False)
            return False
    
    def monitor_system(self, duration: int = 60) -> None:
        """Monitor system for specified duration"""
        self.print_header(f"Monitoring System for {duration} seconds")
        
        start_time = time.time()
        check_interval = 10
        
        while time.time() - start_time < duration:
            print(f"\n📊 Check at {datetime.now().strftime('%H:%M:%S')}")
            
            # Health check
            health = self.check_health()
            
            # Check metrics if available
            try:
                if health.get('fastapi', False):
                    response = requests.get(f"{self.fastapi_url}/monitoring/email-stats", timeout=5)
                    if response.status_code == 200:
                        stats = response.json()
                        print(f"📧 Emails processed (24h): {stats.get('email_processing', {}).get('total_processed_24h', 0)}")
                        print(f"🔔 Active watches: {stats.get('gmail_watches', {}).get('active_watches', 0)}")
            except:
                pass
            
            if time.time() - start_time < duration:
                print(f"⏳ Next check in {check_interval} seconds...")
                time.sleep(check_interval)
        
        print("\n✅ Monitoring completed")
    
    def run_tests(self) -> bool:
        """Run the test suite"""
        self.print_header("Running Test Suite")
        
        try:
            result = subprocess.run([sys.executable, "run_tests.py"], capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("Errors:", result.stderr)
            
            success = result.returncode == 0
            self.print_status(f"Test suite: {'Passed' if success else 'Failed'}", success)
            
            return success
            
        except Exception as e:
            self.print_status(f"Test execution error: {str(e)}", False)
            return False
    
    def show_status(self) -> None:
        """Show comprehensive system status"""
        self.print_header("System Status")
        
        # Health check
        health = self.check_health()
        
        # Show configuration
        print(f"\n📋 Configuration:")
        print(f"   Cloud Run URL: {self.cloud_run_url}")
        print(f"   FastAPI URL: {self.fastapi_url}")
        print(f"   Project ID: {self.project_id}")
        
        # Show service status
        print(f"\n🔧 Services:")
        for service, status in health.items():
            icon = "🟢" if status else "🔴"
            print(f"   {icon} {service.replace('_', ' ').title()}: {'Healthy' if status else 'Unhealthy'}")
        
        # Try to get detailed stats
        if health.get('fastapi', False):
            try:
                response = requests.get(f"{self.fastapi_url}/monitoring/email-stats", timeout=5)
                if response.status_code == 200:
                    stats = response.json()
                    print(f"\n📊 Metrics (24h):")
                    email_stats = stats.get('email_processing', {})
                    print(f"   📧 Emails processed: {email_stats.get('total_processed_24h', 0)}")
                    print(f"   🚀 Real-time: {email_stats.get('by_source', {}).get('gmail_push_notification', 0)}")
                    print(f"   ⏰ Scheduled: {email_stats.get('by_source', {}).get('scheduled', 0)}")
                    print(f"   🔔 Webhooks triggered: {email_stats.get('webhook_triggered', 0)}")
                    
                    watch_stats = stats.get('gmail_watches', {})
                    print(f"\n👁️ Gmail Watches:")
                    print(f"   🟢 Active: {watch_stats.get('active_watches', 0)}")
                    print(f"   🔴 Expired: {watch_stats.get('expired_watches', 0)}")
                    print(f"   📊 Total: {watch_stats.get('total_watches', 0)}")
            except:
                print("\n⚠️ Could not fetch detailed metrics")

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description='Gmail Push Notification System Management Tool')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Health check command
    subparsers.add_parser('health', help='Check system health')
    
    # Deploy command
    deploy_parser = subparsers.add_parser('deploy', help='Deploy Cloud Run service')
    deploy_parser.add_argument('--tag', default='latest', help='Image tag to deploy')
    
    # Setup command
    subparsers.add_parser('setup', help='Set up Pub/Sub topic and subscription')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Send test notification')
    test_parser.add_argument('--email', default='<EMAIL>', help='Test email address')
    test_parser.add_argument('--history-id', default='12345', help='Test history ID')
    
    # Monitor command
    monitor_parser = subparsers.add_parser('monitor', help='Monitor system')
    monitor_parser.add_argument('--duration', type=int, default=60, help='Monitoring duration in seconds')
    
    # Run tests command
    subparsers.add_parser('run-tests', help='Run the test suite')
    
    # Status command
    subparsers.add_parser('status', help='Show comprehensive system status')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = GmailPushManager()
    
    if args.command == 'health':
        manager.check_health()
    elif args.command == 'deploy':
        manager.deploy_cloud_run(args.tag)
    elif args.command == 'setup':
        manager.setup_pubsub()
    elif args.command == 'test':
        manager.test_notification(args.email, args.history_id)
    elif args.command == 'monitor':
        manager.monitor_system(args.duration)
    elif args.command == 'run-tests':
        manager.run_tests()
    elif args.command == 'status':
        manager.show_status()

if __name__ == "__main__":
    main()
