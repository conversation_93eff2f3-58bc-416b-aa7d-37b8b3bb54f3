#!/bin/bash

# Gmail Push Notification System - Quick Start Script
# This script helps developers get started quickly with the system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="ai-email-bot-455814"
REGION="us-central1"

echo -e "${GREEN}🚀 Gmail Push Notification System - Quick Start${NC}"
echo -e "${BLUE}=================================================${NC}"

# Function to print status
print_status() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
        exit 1
    fi
}

# Function to print info
print_info() {
    echo -e "${YELLOW}📋 $1${NC}"
}

# Function to print section header
print_section() {
    echo -e "\n${BLUE}🔧 $1${NC}"
    echo -e "${BLUE}$(printf '%.0s-' {1..40})${NC}"
}

# Check prerequisites
print_section "Checking Prerequisites"

# Check if gcloud is installed
if command -v gcloud &> /dev/null; then
    print_status "gcloud CLI is installed"
else
    echo -e "${RED}❌ gcloud CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if Docker is running
if docker info &> /dev/null; then
    print_status "Docker is running"
else
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Check if Python is available
if command -v python3 &> /dev/null; then
    print_status "Python 3 is available"
else
    echo -e "${RED}❌ Python 3 is not installed.${NC}"
    exit 1
fi

# Set up environment
print_section "Environment Setup"

# Check if .env file exists
if [ ! -f .env ]; then
    print_info "Creating .env file from template..."
    cp .env.example .env
    print_status ".env file created"
    echo -e "${YELLOW}⚠️  Please edit .env file with your actual configuration values${NC}"
    echo -e "${YELLOW}   Required: GEMINI_API_KEY, FASTAPI_BASE_URL${NC}"
else
    print_status ".env file already exists"
fi

# Install Python dependencies for testing
print_info "Installing Python dependencies..."
pip install -q flask requests google-cloud-firestore google-api-python-client google-auth
print_status "Python dependencies installed"

# Google Cloud setup
print_section "Google Cloud Configuration"

# Set project
print_info "Setting Google Cloud project to ${PROJECT_ID}..."
gcloud config set project ${PROJECT_ID}
print_status "Project set to ${PROJECT_ID}"

# Enable required APIs
print_info "Enabling required Google Cloud APIs..."
gcloud services enable cloudbuild.googleapis.com run.googleapis.com pubsub.googleapis.com firestore.googleapis.com
print_status "APIs enabled"

# Create Pub/Sub topic if it doesn't exist
print_info "Creating Pub/Sub topic..."
if gcloud pubsub topics describe email-notifications &> /dev/null; then
    print_status "Pub/Sub topic already exists"
else
    gcloud pubsub topics create email-notifications
    print_status "Pub/Sub topic created"
fi

# Local development setup
print_section "Local Development Setup"

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    print_info "Starting local development environment..."
    
    # Build and start services
    docker-compose build
    print_status "Docker images built"
    
    echo -e "${YELLOW}📋 Starting services in background...${NC}"
    docker-compose up -d
    
    # Wait a moment for services to start
    sleep 5
    
    # Check if services are running
    if curl -s http://localhost:8080/health > /dev/null; then
        print_status "Cloud Run handler is running on port 8080"
    else
        echo -e "${YELLOW}⚠️  Cloud Run handler may still be starting...${NC}"
    fi
    
    if curl -s http://localhost:8000/ > /dev/null; then
        print_status "FastAPI backend is running on port 8000"
    else
        echo -e "${YELLOW}⚠️  FastAPI backend may still be starting...${NC}"
    fi
    
else
    echo -e "${YELLOW}⚠️  Docker Compose not available. Starting services manually...${NC}"
    
    # Start Cloud Run handler
    print_info "Starting Cloud Run handler..."
    python cloud_run_handler.py &
    CLOUD_RUN_PID=$!
    
    # Start FastAPI backend
    print_info "Starting FastAPI backend..."
    cd backend && python main.py &
    FASTAPI_PID=$!
    cd ..
    
    # Wait for services to start
    sleep 3
    print_status "Services started manually"
fi

# Run tests
print_section "Running Tests"

print_info "Running test suite..."
python run_tests.py
print_status "Tests completed"

# Send test notification
print_section "Testing Notification System"

print_info "Sending test notification..."
python mock_pubsub_messages.py --mode test --email <EMAIL>
print_status "Test notification sent"

# Show status
print_section "System Status"

print_info "Checking system status..."
python manage_gmail_push.py status

# Final instructions
print_section "Next Steps"

echo -e "${GREEN}🎉 Quick start completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 What's running:${NC}"
echo -e "   🔧 Cloud Run Handler: http://localhost:8080"
echo -e "   🔧 FastAPI Backend: http://localhost:8000"
echo ""
echo -e "${YELLOW}🧪 Testing:${NC}"
echo -e "   • Run tests: ${BLUE}python run_tests.py${NC}"
echo -e "   • Send test notification: ${BLUE}python mock_pubsub_messages.py --mode test${NC}"
echo -e "   • Monitor system: ${BLUE}python manage_gmail_push.py monitor${NC}"
echo ""
echo -e "${YELLOW}🚀 Production Deployment:${NC}"
echo -e "   • Deploy to Cloud Run: ${BLUE}./deploy-cloudrun.sh${NC}"
echo -e "   • Or use management tool: ${BLUE}python manage_gmail_push.py deploy${NC}"
echo ""
echo -e "${YELLOW}📚 Documentation:${NC}"
echo -e "   • Setup Guide: ${BLUE}SETUP_GUIDE.md${NC}"
echo -e "   • Deployment Checklist: ${BLUE}DEPLOYMENT_CHECKLIST.md${NC}"
echo -e "   • Implementation Summary: ${BLUE}IMPLEMENTATION_SUMMARY.md${NC}"
echo ""
echo -e "${YELLOW}⚠️  Important:${NC}"
echo -e "   • Edit .env file with your actual configuration"
echo -e "   • Set up Gmail API credentials"
echo -e "   • Configure Gemini AI API key"
echo -e "   • Update FASTAPI_BASE_URL for production"
echo ""
echo -e "${GREEN}✨ Happy coding!${NC}"

# Cleanup function for manual start
cleanup() {
    if [ ! -z "$CLOUD_RUN_PID" ]; then
        kill $CLOUD_RUN_PID 2>/dev/null || true
    fi
    if [ ! -z "$FASTAPI_PID" ]; then
        kill $FASTAPI_PID 2>/dev/null || true
    fi
}

# Set up cleanup on script exit if we started services manually
if [ ! -z "$CLOUD_RUN_PID" ] || [ ! -z "$FASTAPI_PID" ]; then
    trap cleanup EXIT
fi
