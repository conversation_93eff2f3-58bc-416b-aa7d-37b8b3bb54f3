#!/usr/bin/env python3
"""
Integration tests for Gmail Push Notification System

Tests the Gmail API integration including:
- Watch setup and management
- Credential handling
- Gmail API calls
- Firestore integration
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import os
import sys
from datetime import datetime, timedelta

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Gmail push setup module
import gmail_push_setup

class TestGmailWatchManager(unittest.TestCase):
    """Test cases for Gmail Watch Manager"""
    
    def setUp(self):
        """Set up test fixtures"""
        with patch('gmail_push_setup.firestore.Client'):
            self.manager = gmail_push_setup.GmailWatchManager()
        
        # Mock Firestore client
        self.mock_db = Mock()
        self.manager.db = self.mock_db
        
        # Sample credentials
        self.sample_credentials = {
            'token': 'test-token',
            'refresh_token': 'test-refresh-token',
            'token_uri': 'https://oauth2.googleapis.com/token',
            'client_id': 'test-client-id',
            'client_secret': 'test-client-secret',
            'scopes': ['https://www.googleapis.com/auth/gmail.readonly']
        }
        
        # Sample watch response
        self.sample_watch_response = {
            'historyId': '12345',
            'expiration': str(int((datetime.now() + timedelta(days=7)).timestamp() * 1000))
        }
    
    @patch('gmail_push_setup.Credentials')
    @patch('gmail_push_setup.GoogleRequest')
    def test_refresh_credentials_success(self, mock_google_request, mock_credentials):
        """Test successful credential refresh"""
        # Mock valid credentials
        mock_creds = Mock()
        mock_creds.valid = True
        mock_credentials.return_value = mock_creds
        
        result = self.manager.refresh_credentials_if_needed(self.sample_credentials)
        
        self.assertIsNotNone(result)
        self.assertEqual(result, mock_creds)
    
    @patch('gmail_push_setup.Credentials')
    def test_refresh_credentials_expired(self, mock_credentials):
        """Test credential refresh when expired"""
        # Mock expired credentials that need refresh
        mock_creds = Mock()
        mock_creds.valid = False
        mock_creds.expired = True
        mock_creds.refresh_token = 'test-refresh-token'
        mock_creds.refresh = Mock()
        mock_credentials.return_value = mock_creds
        
        result = self.manager.refresh_credentials_if_needed(self.sample_credentials)
        
        self.assertIsNotNone(result)
        mock_creds.refresh.assert_called_once()
    
    @patch('gmail_push_setup.build')
    def test_setup_watch_success(self, mock_build):
        """Test successful Gmail watch setup"""
        # Mock Gmail service
        mock_service = Mock()
        mock_profile = {'emailAddress': '<EMAIL>'}
        mock_service.users().getProfile().execute.return_value = mock_profile
        mock_service.users().stop().execute.side_effect = Exception("No existing watch")
        mock_service.users().watch().execute.return_value = self.sample_watch_response
        mock_build.return_value = mock_service
        
        # Mock credential refresh
        with patch.object(self.manager, 'refresh_credentials_if_needed') as mock_refresh:
            mock_creds = Mock()
            mock_refresh.return_value = mock_creds
            
            # Mock store_watch_details
            with patch.object(self.manager, 'store_watch_details') as mock_store:
                mock_store.return_value = True
                
                result = self.manager.setup_watch_for_user('user-123', 'account-123', self.sample_credentials)
                
                self.assertIsNotNone(result)
                self.assertEqual(result['email_address'], '<EMAIL>')
                self.assertEqual(result['historyId'], '12345')
                self.assertEqual(result['user_id'], 'user-123')
                self.assertEqual(result['account_id'], 'account-123')
    
    def test_store_watch_details_success(self):
        """Test successful watch details storage"""
        # Mock Firestore operations
        mock_watch_ref = Mock()
        mock_collection_ref = Mock()
        mock_collection_ref.document.return_value = mock_watch_ref
        mock_user_ref = Mock()
        mock_user_ref.collection.return_value = mock_collection_ref
        mock_users_ref = Mock()
        mock_users_ref.document.return_value = mock_user_ref
        
        self.mock_db.collection.return_value = mock_users_ref
        
        watch_metadata = {
            'historyId': '12345',
            'expiration': **********,
            'email_address': '<EMAIL>',
            'status': 'active'
        }
        
        result = self.manager.store_watch_details('user-123', 'account-123', watch_metadata)
        
        self.assertTrue(result)
        mock_watch_ref.set.assert_called_once_with(watch_metadata)
    
    def test_get_expiring_watches(self):
        """Test getting expiring watches"""
        # Mock Firestore query for expiring watches
        mock_watch_doc = Mock()
        mock_watch_doc.id = 'account-123'
        mock_watch_doc.to_dict.return_value = {
            'historyId': '12345',
            'expiration': (datetime.now() + timedelta(hours=12)).timestamp(),
            'email_address': '<EMAIL>',
            'status': 'active'
        }
        
        mock_watches_query = Mock()
        mock_watches_query.stream.return_value = [mock_watch_doc]
        
        mock_watches_ref = Mock()
        mock_watches_ref.where.return_value.where.return_value = mock_watches_query
        
        mock_user_doc = Mock()
        mock_user_doc.id = 'user-123'
        
        mock_user_ref = Mock()
        mock_user_ref.collection.return_value = mock_watches_ref
        
        mock_users_ref = Mock()
        mock_users_ref.document.return_value = mock_user_ref
        mock_users_ref.stream.return_value = [mock_user_doc]
        
        self.mock_db.collection.return_value = mock_users_ref
        
        result = self.manager.get_expiring_watches(24)
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['user_id'], 'user-123')
        self.assertEqual(result[0]['account_id'], 'account-123')
    
    def test_renew_watch_success(self):
        """Test successful watch renewal"""
        # Mock account document
        mock_account_doc = Mock()
        mock_account_doc.exists = True
        mock_account_doc.to_dict.return_value = {
            'credentials': self.sample_credentials
        }
        
        mock_account_ref = Mock()
        mock_account_ref.get.return_value = mock_account_doc
        
        mock_collection_ref = Mock()
        mock_collection_ref.document.return_value = mock_account_ref
        
        mock_user_ref = Mock()
        mock_user_ref.collection.return_value = mock_collection_ref
        
        mock_users_ref = Mock()
        mock_users_ref.document.return_value = mock_user_ref
        
        self.mock_db.collection.return_value = mock_users_ref
        
        # Mock setup_watch_for_user
        with patch.object(self.manager, 'setup_watch_for_user') as mock_setup:
            mock_setup.return_value = {'success': True}
            
            result = self.manager.renew_watch('user-123', 'account-123')
            
            self.assertTrue(result)
            mock_setup.assert_called_once_with('user-123', 'account-123', self.sample_credentials)
    
    def test_setup_all_user_watches(self):
        """Test setting up watches for all users"""
        # Mock user and account documents
        mock_account_doc = Mock()
        mock_account_doc.id = 'account-123'
        mock_account_doc.to_dict.return_value = {
            'credentials': self.sample_credentials
        }
        
        mock_user_doc = Mock()
        mock_user_doc.id = 'user-123'
        
        # Mock Firestore structure
        mock_accounts_ref = Mock()
        mock_accounts_ref.stream.return_value = [mock_account_doc]
        
        mock_watches_ref = Mock()
        mock_existing_watch = Mock()
        mock_existing_watch.exists = False
        mock_watches_ref.document.return_value.get.return_value = mock_existing_watch
        
        mock_user_ref = Mock()
        mock_user_ref.collection.side_effect = lambda name: {
            'email_accounts': mock_accounts_ref,
            'gmail_watches': mock_watches_ref
        }[name]
        
        mock_users_ref = Mock()
        mock_users_ref.document.return_value = mock_user_ref
        mock_users_ref.stream.return_value = [mock_user_doc]
        
        self.mock_db.collection.return_value = mock_users_ref
        
        # Mock setup_watch_for_user
        with patch.object(self.manager, 'setup_watch_for_user') as mock_setup:
            mock_setup.return_value = {'success': True}
            
            result = self.manager.setup_all_user_watches()
            
            self.assertTrue(result['success'])
            self.assertEqual(result['setup_count'], 1)
            self.assertEqual(result['failed_count'], 0)
    
    def test_get_watch_status(self):
        """Test getting watch status"""
        # Mock watch documents
        mock_watch_doc = Mock()
        mock_watch_doc.id = 'account-123'
        mock_watch_doc.to_dict.return_value = {
            'email_address': '<EMAIL>',
            'status': 'active',
            'expiration': (datetime.now() + timedelta(days=3)).timestamp(),
            'created_at': datetime.now().isoformat()
        }
        
        mock_user_doc = Mock()
        mock_user_doc.id = 'user-123'
        
        # Mock Firestore structure
        mock_watches_ref = Mock()
        mock_watches_ref.stream.return_value = [mock_watch_doc]
        
        mock_user_ref = Mock()
        mock_user_ref.collection.return_value = mock_watches_ref
        
        mock_users_ref = Mock()
        mock_users_ref.document.return_value = mock_user_ref
        mock_users_ref.stream.return_value = [mock_user_doc]
        
        self.mock_db.collection.return_value = mock_users_ref
        
        result = self.manager.get_watch_status()
        
        self.assertTrue(result['success'])
        self.assertEqual(result['total_users'], 1)
        self.assertEqual(result['active_watches'], 1)
        self.assertEqual(result['expired_watches'], 0)

class TestGmailPushIntegration(unittest.TestCase):
    """Integration tests for the complete Gmail push system"""
    
    def setUp(self):
        """Set up integration test fixtures"""
        self.test_email = '<EMAIL>'
        self.test_user_id = 'test-user-123'
        self.test_account_id = 'gmail-account-123'
    
    @patch('gmail_push_setup.firestore.Client')
    @patch('gmail_push_setup.build')
    def test_end_to_end_watch_setup(self, mock_build, mock_firestore_client):
        """Test end-to-end watch setup process"""
        # Mock Gmail service
        mock_service = Mock()
        mock_service.users().getProfile().execute.return_value = {
            'emailAddress': self.test_email
        }
        mock_service.users().stop().execute.side_effect = Exception("No existing watch")
        mock_service.users().watch().execute.return_value = {
            'historyId': '12345',
            'expiration': str(int((datetime.now() + timedelta(days=7)).timestamp() * 1000))
        }
        mock_build.return_value = mock_service
        
        # Mock Firestore
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        # Create manager and test watch setup
        manager = gmail_push_setup.GmailWatchManager()
        
        with patch.object(manager, 'store_watch_details') as mock_store:
            mock_store.return_value = True
            
            result = manager.setup_watch_for_user(
                self.test_user_id, 
                self.test_account_id, 
                {
                    'token': 'test-token',
                    'refresh_token': 'test-refresh-token',
                    'token_uri': 'https://oauth2.googleapis.com/token',
                    'client_id': 'test-client-id',
                    'client_secret': 'test-client-secret',
                    'scopes': ['https://www.googleapis.com/auth/gmail.readonly']
                }
            )
            
            self.assertIsNotNone(result)
            self.assertEqual(result['email_address'], self.test_email)
            self.assertEqual(result['user_id'], self.test_user_id)
            self.assertEqual(result['account_id'], self.test_account_id)
            
            # Verify Gmail API calls
            mock_service.users().getProfile().execute.assert_called_once()
            mock_service.users().watch().execute.assert_called_once()
            
            # Verify watch request structure
            watch_call = mock_service.users().watch.call_args[1]['body']
            self.assertEqual(watch_call['topicName'], 'projects/ai-email-bot-455814/topics/email-notifications')
            self.assertEqual(watch_call['labelIds'], ['INBOX'])

if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
