#!/usr/bin/env python3
"""
Test Runner for Gmail Push Notification System

Runs all tests for the real-time email processing system including:
- Unit tests for Cloud Run handler
- Integration tests for Gmail API
- End-to-end pipeline tests
- Mock message testing
"""

import unittest
import sys
import os
import time
import subprocess
from datetime import datetime

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def print_section(title):
    """Print a formatted section header"""
    print(f"\n📋 {title}")
    print("-" * 40)

def run_unit_tests():
    """Run unit tests"""
    print_section("Running Unit Tests")
    
    test_modules = [
        'test_cloud_run_handler',
        'test_gmail_integration'
    ]
    
    suite = unittest.TestSuite()
    
    for module_name in test_modules:
        try:
            module = __import__(module_name)
            suite.addTests(unittest.defaultTestLoader.loadTestsFromModule(module))
            print(f"✅ Loaded tests from {module_name}")
        except ImportError as e:
            print(f"❌ Failed to import {module_name}: {str(e)}")
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_integration_tests():
    """Run integration tests"""
    print_section("Running Integration Tests")
    
    try:
        # Import and run integration tests
        import test_email_pipeline
        
        suite = unittest.defaultTestLoader.loadTestsFromModule(test_email_pipeline)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        print(f"❌ Failed to import integration tests: {str(e)}")
        return False

def test_mock_messages():
    """Test mock message generation"""
    print_section("Testing Mock Message Generation")
    
    try:
        from mock_pubsub_messages import MockPubSubMessageGenerator
        
        generator = MockPubSubMessageGenerator()
        
        # Test notification generation
        print("📧 Testing notification generation...")
        notification = generator.generate_gmail_notification()
        print(f"✅ Generated notification: {notification}")
        
        # Test Pub/Sub message creation
        print("📨 Testing Pub/Sub message creation...")
        pubsub_message = generator.create_pubsub_message(notification)
        print(f"✅ Generated Pub/Sub message with ID: {pubsub_message['message']['messageId']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock message testing failed: {str(e)}")
        return False

def test_service_connectivity():
    """Test connectivity to services"""
    print_section("Testing Service Connectivity")
    
    services = [
        ("Cloud Run Handler", "http://localhost:8080/health"),
        ("FastAPI Backend", "http://localhost:8000/")
    ]
    
    connectivity_results = []
    
    for service_name, url in services:
        try:
            import requests
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {service_name} is accessible")
                connectivity_results.append(True)
            else:
                print(f"⚠️  {service_name} returned status {response.status_code}")
                connectivity_results.append(False)
        except Exception as e:
            print(f"❌ {service_name} not accessible: {str(e)}")
            connectivity_results.append(False)
    
    return all(connectivity_results)

def run_live_test():
    """Run a live test with mock messages"""
    print_section("Running Live Test with Mock Messages")
    
    try:
        from mock_pubsub_messages import MockPubSubMessageGenerator
        
        generator = MockPubSubMessageGenerator()
        
        # Test health check first
        if not generator.test_health_check():
            print("❌ Health check failed, skipping live test")
            return False
        
        # Send a test notification
        print("📧 Sending test notification...")
        result = generator.send_test_notification("<EMAIL>", "12345")
        
        if result.get('success', False):
            print("✅ Live test passed")
            return True
        else:
            print(f"❌ Live test failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Live test failed: {str(e)}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print_section("Checking Dependencies")
    
    required_packages = [
        'flask',
        'requests',
        'google-cloud-firestore',
        'google-api-python-client',
        'google-auth'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def main():
    """Main test runner"""
    print_header("Gmail Push Notification System Test Suite")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Track test results
    test_results = {}
    
    # Check dependencies first
    test_results['dependencies'] = check_dependencies()
    if not test_results['dependencies']:
        print("\n❌ Dependency check failed. Please install missing packages.")
        return False
    
    # Test service connectivity
    test_results['connectivity'] = test_service_connectivity()
    
    # Run unit tests
    test_results['unit_tests'] = run_unit_tests()
    
    # Run integration tests
    test_results['integration_tests'] = run_integration_tests()
    
    # Test mock messages
    test_results['mock_messages'] = test_mock_messages()
    
    # Run live test if services are available
    if test_results['connectivity']:
        test_results['live_test'] = run_live_test()
    else:
        print("\n⚠️  Skipping live test due to service connectivity issues")
        test_results['live_test'] = False
    
    # Print summary
    print_header("Test Summary")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    print(f"\n📊 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
