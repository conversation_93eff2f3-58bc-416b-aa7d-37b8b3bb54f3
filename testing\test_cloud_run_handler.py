#!/usr/bin/env python3
"""
Unit tests for Cloud Run Handler

Tests the Gmail push notification handler functionality including:
- Pub/Sub message parsing
- User lookup in Firestore
- Gmail API integration
- FastAPI communication
- Error handling
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import base64
import os
import sys

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the handler
import cloud_run_handler

class TestCloudRunHandler(unittest.TestCase):
    """Test cases for Cloud Run Handler"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.processor = cloud_run_handler.GmailNotificationProcessor()
        
        # Mock Firestore client
        self.mock_db = Mock()
        self.processor.db = self.mock_db
        
        # Sample notification data
        self.sample_notification = {
            'emailAddress': '<EMAIL>',
            'historyId': '12345'
        }
        
        # Sample Pub/Sub message
        self.sample_pubsub_message = {
            'message': {
                'data': base64.b64encode(json.dumps(self.sample_notification).encode()).decode(),
                'messageId': 'test-message-id',
                'publishTime': '2023-01-01T00:00:00.000Z'
            }
        }
        
        # Sample user data
        self.sample_user_data = {
            'user_id': 'test-user-123',
            'user_data': {'email': '<EMAIL>'},
            'account_id': 'gmail-account-123',
            'account_data': {
                'email': '<EMAIL>',
                'credentials': {
                    'token': 'test-token',
                    'refresh_token': 'test-refresh-token',
                    'token_uri': 'https://oauth2.googleapis.com/token',
                    'client_id': 'test-client-id',
                    'client_secret': 'test-client-secret',
                    'scopes': ['https://www.googleapis.com/auth/gmail.readonly']
                }
            },
            'email_address': '<EMAIL>'
        }
    
    def test_parse_pubsub_message_success(self):
        """Test successful Pub/Sub message parsing"""
        result = self.processor.parse_pubsub_message(self.sample_pubsub_message)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['emailAddress'], '<EMAIL>')
        self.assertEqual(result['historyId'], '12345')
    
    def test_parse_pubsub_message_no_message(self):
        """Test Pub/Sub message parsing with no message"""
        request_data = {}
        result = self.processor.parse_pubsub_message(request_data)
        
        self.assertIsNone(result)
    
    def test_parse_pubsub_message_no_data(self):
        """Test Pub/Sub message parsing with no data"""
        request_data = {'message': {}}
        result = self.processor.parse_pubsub_message(request_data)
        
        self.assertIsNone(result)
    
    def test_parse_pubsub_message_invalid_base64(self):
        """Test Pub/Sub message parsing with invalid base64"""
        request_data = {
            'message': {
                'data': 'invalid-base64-data'
            }
        }
        result = self.processor.parse_pubsub_message(request_data)
        
        self.assertIsNone(result)
    
    @patch('cloud_run_handler.firestore')
    def test_get_user_by_email_success(self, mock_firestore):
        """Test successful user lookup by email"""
        # Mock Firestore query
        mock_user_doc = Mock()
        mock_user_doc.id = 'test-user-123'
        mock_user_doc.to_dict.return_value = {'email': '<EMAIL>'}
        
        mock_account_doc = Mock()
        mock_account_doc.id = 'gmail-account-123'
        mock_account_doc.to_dict.return_value = self.sample_user_data['account_data']
        
        mock_accounts_query = Mock()
        mock_accounts_query.stream.return_value = [mock_account_doc]
        
        mock_accounts_ref = Mock()
        mock_accounts_ref.where.return_value = mock_accounts_query
        
        mock_user_ref = Mock()
        mock_user_ref.collection.return_value = mock_accounts_ref
        
        mock_users_ref = Mock()
        mock_users_ref.document.return_value = mock_user_ref
        mock_users_ref.stream.return_value = [mock_user_doc]
        
        self.mock_db.collection.return_value = mock_users_ref
        
        result = self.processor.get_user_by_email('<EMAIL>')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['user_id'], 'test-user-123')
        self.assertEqual(result['account_id'], 'gmail-account-123')
        self.assertEqual(result['email_address'], '<EMAIL>')
    
    def test_get_user_by_email_not_found(self):
        """Test user lookup when user not found"""
        # Mock empty query results
        mock_users_ref = Mock()
        mock_users_ref.stream.return_value = []
        
        self.mock_db.collection.return_value = mock_users_ref
        
        result = self.processor.get_user_by_email('<EMAIL>')
        
        self.assertIsNone(result)
    
    @patch('cloud_run_handler.Credentials')
    @patch('cloud_run_handler.GoogleRequest')
    def test_refresh_credentials_success(self, mock_google_request, mock_credentials):
        """Test successful credential refresh"""
        # Mock credentials
        mock_creds = Mock()
        mock_creds.valid = True
        mock_credentials.return_value = mock_creds
        
        credentials_dict = self.sample_user_data['account_data']['credentials']
        result = self.processor.refresh_credentials_if_needed(credentials_dict)
        
        self.assertIsNotNone(result)
        self.assertEqual(result, mock_creds)
    
    @patch('cloud_run_handler.Credentials')
    def test_refresh_credentials_expired(self, mock_credentials):
        """Test credential refresh when expired"""
        # Mock expired credentials
        mock_creds = Mock()
        mock_creds.valid = False
        mock_creds.expired = True
        mock_creds.refresh_token = 'test-refresh-token'
        mock_creds.refresh = Mock()
        mock_credentials.return_value = mock_creds
        
        credentials_dict = self.sample_user_data['account_data']['credentials']
        result = self.processor.refresh_credentials_if_needed(credentials_dict)
        
        self.assertIsNotNone(result)
        mock_creds.refresh.assert_called_once()
    
    @patch('cloud_run_handler.requests.post')
    def test_trigger_email_analysis_success(self, mock_post):
        """Test successful email analysis trigger"""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        result = self.processor.trigger_email_analysis('email-123', 'user-123', 'account-123')
        
        self.assertTrue(result)
        mock_post.assert_called_once()
        
        # Check the request payload
        call_args = mock_post.call_args
        payload = call_args[1]['json']
        self.assertEqual(payload['email_id'], 'email-123')
        self.assertEqual(payload['user_id'], 'user-123')
        self.assertEqual(payload['account_id'], 'account-123')
        self.assertEqual(payload['source'], 'gmail_push_notification')
    
    @patch('cloud_run_handler.requests.post')
    def test_trigger_email_analysis_failure(self, mock_post):
        """Test email analysis trigger failure"""
        # Mock failed HTTP response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = 'Internal Server Error'
        mock_post.return_value = mock_response
        
        result = self.processor.trigger_email_analysis('email-123', 'user-123', 'account-123')
        
        self.assertFalse(result)
    
    @patch('cloud_run_handler.build')
    def test_fetch_new_emails_success(self, mock_build):
        """Test successful email fetching"""
        # Mock Gmail service
        mock_service = Mock()
        mock_history_response = {
            'history': [
                {
                    'messagesAdded': [
                        {
                            'message': {
                                'id': 'email-123',
                                'threadId': 'thread-123',
                                'labelIds': ['INBOX']
                            }
                        }
                    ]
                }
            ]
        }
        
        mock_service.users().history().list().execute.return_value = mock_history_response
        mock_build.return_value = mock_service
        
        # Mock credentials refresh
        with patch.object(self.processor, 'refresh_credentials_if_needed') as mock_refresh:
            mock_creds = Mock()
            mock_refresh.return_value = mock_creds
            
            result = self.processor.fetch_new_emails(self.sample_user_data, '12345')
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]['email_id'], 'email-123')
            self.assertEqual(result[0]['user_id'], 'test-user-123')
            self.assertEqual(result[0]['account_id'], 'gmail-account-123')

class TestFlaskApp(unittest.TestCase):
    """Test cases for Flask application endpoints"""
    
    def setUp(self):
        """Set up test client"""
        cloud_run_handler.app.config['TESTING'] = True
        self.client = cloud_run_handler.app.test_client()
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = self.client.get('/health')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('status', data)
        self.assertIn('timestamp', data)
    
    def test_root_endpoint(self):
        """Test root endpoint"""
        response = self.client.get('/')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('service', data)
        self.assertIn('version', data)
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'development'})
    def test_test_notification_endpoint(self):
        """Test notification endpoint in development mode"""
        test_data = {
            'emailAddress': '<EMAIL>',
            'historyId': '12345'
        }
        
        with patch.object(cloud_run_handler.processor, 'process_gmail_notification') as mock_process:
            mock_process.return_value = {
                'success': True,
                'message': 'Test processed successfully',
                'emails_processed': 1
            }
            
            response = self.client.post('/test-notification', 
                                      data=json.dumps(test_data),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertTrue(data['test_mode'])
            self.assertTrue(data['result']['success'])

if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
