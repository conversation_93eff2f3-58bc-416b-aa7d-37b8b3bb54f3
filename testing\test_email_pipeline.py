#!/usr/bin/env python3
"""
End-to-End Email Processing Pipeline Tests

Tests the complete flow from Gmail push notification to email analysis:
1. Gmail notification → Cloud Run handler
2. Cloud Run handler → FastAPI processing
3. FastAPI → Email analysis with Gemini
4. Analysis results → Webhook delivery
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import time
import requests
import os
import sys
from datetime import datetime

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestEmailProcessingPipeline(unittest.TestCase):
    """End-to-end tests for email processing pipeline"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.cloud_run_url = "http://localhost:8080"
        self.fastapi_url = "http://localhost:8000"
        
        # Test data
        self.test_email_id = "test-email-123"
        self.test_user_id = "test-user-123"
        self.test_account_id = "gmail-account-123"
        self.test_email_address = "<EMAIL>"
        self.test_history_id = "12345"
        
        # Sample email data
        self.sample_email_data = {
            "id": self.test_email_id,
            "threadId": "thread-123",
            "labelIds": ["INBOX"],
            "snippet": "Test email for purchase order processing",
            "payload": {
                "headers": [
                    {"name": "From", "value": "<EMAIL>"},
                    {"name": "To", "value": self.test_email_address},
                    {"name": "Subject", "value": "Purchase Order #PO-12345"}
                ],
                "body": {
                    "data": "VGVzdCBlbWFpbCBib2R5IGZvciBwdXJjaGFzZSBvcmRlcg=="  # Base64 encoded
                }
            },
            "has_attachments": False,
            "attachments": []
        }
        
        # Sample analysis result
        self.sample_analysis_result = {
            "category": "purchase_order",
            "analysis_results": {
                "summary": "Purchase order for office supplies",
                "sentiment": "neutral",
                "key_information": {
                    "po_number": "PO-12345",
                    "supplier": "<EMAIL>",
                    "amount": "$500.00"
                }
            },
            "confidence_score": 0.95,
            "error": False
        }
    
    def test_health_checks(self):
        """Test health checks for both services"""
        # Test Cloud Run handler health
        try:
            response = requests.get(f"{self.cloud_run_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Cloud Run handler is healthy")
            else:
                print(f"⚠️  Cloud Run handler health check returned {response.status_code}")
        except Exception as e:
            print(f"❌ Cloud Run handler not accessible: {str(e)}")
        
        # Test FastAPI health
        try:
            response = requests.get(f"{self.fastapi_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ FastAPI backend is healthy")
            else:
                print(f"⚠️  FastAPI backend health check returned {response.status_code}")
        except Exception as e:
            print(f"❌ FastAPI backend not accessible: {str(e)}")
    
    @patch('requests.post')
    def test_cloud_run_to_fastapi_communication(self, mock_post):
        """Test communication from Cloud Run handler to FastAPI"""
        # Mock successful FastAPI response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Email processing started",
            "email_id": self.test_email_id
        }
        mock_post.return_value = mock_response
        
        # Import and test the processor
        import cloud_run_handler
        processor = cloud_run_handler.GmailNotificationProcessor()
        
        result = processor.trigger_email_analysis(
            self.test_email_id,
            self.test_user_id,
            self.test_account_id
        )
        
        self.assertTrue(result)
        
        # Verify the request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        # Check URL
        expected_url = f"{processor.fastapi_base_url}/emails/process-single"
        self.assertEqual(call_args[0][0], expected_url)
        
        # Check payload
        payload = call_args[1]['json']
        self.assertEqual(payload['email_id'], self.test_email_id)
        self.assertEqual(payload['user_id'], self.test_user_id)
        self.assertEqual(payload['account_id'], self.test_account_id)
        self.assertEqual(payload['source'], 'gmail_push_notification')
    
    def test_gmail_notification_processing(self):
        """Test processing of Gmail push notifications"""
        import cloud_run_handler
        
        processor = cloud_run_handler.GmailNotificationProcessor()
        
        # Mock the dependencies
        with patch.object(processor, 'get_user_by_email') as mock_get_user, \
             patch.object(processor, 'fetch_new_emails') as mock_fetch_emails, \
             patch.object(processor, 'trigger_email_analysis') as mock_trigger:
            
            # Mock user lookup
            mock_get_user.return_value = {
                'user_id': self.test_user_id,
                'account_id': self.test_account_id,
                'email_address': self.test_email_address
            }
            
            # Mock email fetching
            mock_fetch_emails.return_value = [
                {
                    'email_id': self.test_email_id,
                    'user_id': self.test_user_id,
                    'account_id': self.test_account_id
                }
            ]
            
            # Mock analysis triggering
            mock_trigger.return_value = True
            
            # Test notification processing
            notification_data = {
                'emailAddress': self.test_email_address,
                'historyId': self.test_history_id
            }
            
            result = processor.process_gmail_notification(notification_data)
            
            self.assertTrue(result['success'])
            self.assertEqual(result['emails_processed'], 1)
            self.assertEqual(result['successful_triggers'], 1)
            
            # Verify method calls
            mock_get_user.assert_called_once_with(self.test_email_address)
            mock_fetch_emails.assert_called_once()
            mock_trigger.assert_called_once_with(
                self.test_email_id,
                self.test_user_id,
                self.test_account_id
            )
    
    def test_single_email_processing_request(self):
        """Test the single email processing request structure"""
        from backend.routers.batch_email_router import SingleEmailProcessRequest
        
        # Test valid request
        request_data = {
            "email_id": self.test_email_id,
            "user_id": self.test_user_id,
            "account_id": self.test_account_id,
            "priority": "real_time",
            "source": "gmail_push_notification",
            "force_reanalysis": False
        }
        
        request = SingleEmailProcessRequest(**request_data)
        
        self.assertEqual(request.email_id, self.test_email_id)
        self.assertEqual(request.user_id, self.test_user_id)
        self.assertEqual(request.account_id, self.test_account_id)
        self.assertEqual(request.source, "gmail_push_notification")
        self.assertFalse(request.force_reanalysis)
    
    @patch('backend.routers.batch_email_router.firestore.client')
    @patch('backend.routers.batch_email_router.get_gmail_service')
    @patch('backend.routers.batch_email_router.get_message_details')
    @patch('backend.routers.batch_email_router.analyze_with_gemini_direct')
    def test_background_email_processing(self, mock_gemini, mock_get_message, mock_gmail_service, mock_firestore):
        """Test background email processing function"""
        # Mock Firestore
        mock_db = Mock()
        mock_firestore.return_value = mock_db
        
        # Mock account document
        mock_account_doc = Mock()
        mock_account_doc.exists = True
        mock_account_doc.to_dict.return_value = {
            'credentials': {
                'token': 'test-token',
                'refresh_token': 'test-refresh-token'
            }
        }
        
        mock_account_ref = Mock()
        mock_account_ref.get.return_value = mock_account_doc
        
        # Mock Firestore structure
        mock_collection = Mock()
        mock_collection.document.return_value = mock_account_ref
        mock_user_ref = Mock()
        mock_user_ref.collection.return_value = mock_collection
        mock_db.collection.return_value.document.return_value = mock_user_ref
        
        # Mock Gmail service and message details
        mock_service = Mock()
        mock_gmail_service.return_value = mock_service
        mock_get_message.return_value = self.sample_email_data
        
        # Mock Gemini analysis
        mock_gemini.return_value = self.sample_analysis_result
        
        # Mock email storage
        with patch('backend.routers.batch_email_router.email_storage.store_raw_email') as mock_store:
            mock_store.return_value = True
            
            # Import and test the background function
            from backend.routers.batch_email_router import process_single_email_background
            import asyncio
            
            # Run the background processing
            asyncio.run(process_single_email_background(
                self.test_email_id,
                self.test_user_id,
                self.test_account_id,
                "gmail_push_notification",
                False
            ))
            
            # Verify calls were made
            mock_gmail_service.assert_called_once()
            mock_get_message.assert_called_once_with(mock_service, msg_id=self.test_email_id)
            mock_gemini.assert_called_once()
    
    def test_webhook_trigger_conditions(self):
        """Test webhook triggering for different email categories"""
        webhook_categories = ['purchase_order', 'invoice', 'order_confirmation', 'order']
        non_webhook_categories = ['other', 'personal', 'newsletter', 'spam']
        
        # Test categories that should trigger webhooks
        for category in webhook_categories:
            with self.subTest(category=category):
                analysis_result = {**self.sample_analysis_result, 'category': category}
                
                # In a real test, we would check if webhook was called
                # For now, we just verify the category is in the webhook list
                self.assertIn(category, webhook_categories)
        
        # Test categories that should NOT trigger webhooks
        for category in non_webhook_categories:
            with self.subTest(category=category):
                self.assertNotIn(category, webhook_categories)
    
    def test_error_handling(self):
        """Test error handling in the pipeline"""
        import cloud_run_handler
        
        processor = cloud_run_handler.GmailNotificationProcessor()
        
        # Test with invalid notification data
        invalid_notification = {}
        result = processor.process_gmail_notification(invalid_notification)
        
        self.assertFalse(result['success'])
        self.assertIn('error', result)
        
        # Test with missing email address
        invalid_notification = {'historyId': '12345'}
        result = processor.process_gmail_notification(invalid_notification)
        
        self.assertFalse(result['success'])
        self.assertIn('Missing', result['error'])

class TestLocalDevelopmentSetup(unittest.TestCase):
    """Tests for local development setup"""
    
    def test_environment_variables(self):
        """Test that required environment variables are set for testing"""
        required_vars = [
            'FASTAPI_BASE_URL',
            'GOOGLE_CLOUD_PROJECT',
            'PUBSUB_TOPIC'
        ]
        
        for var in required_vars:
            # Check if variable is set (either in environment or has default)
            value = os.getenv(var)
            if not value:
                print(f"⚠️  Environment variable {var} not set, using default")
    
    def test_mock_message_generator(self):
        """Test the mock Pub/Sub message generator"""
        from mock_pubsub_messages import MockPubSubMessageGenerator
        
        generator = MockPubSubMessageGenerator()
        
        # Test notification generation
        notification = generator.generate_gmail_notification()
        self.assertIn('emailAddress', notification)
        self.assertIn('historyId', notification)
        
        # Test Pub/Sub message creation
        pubsub_message = generator.create_pubsub_message(notification)
        self.assertIn('message', pubsub_message)
        self.assertIn('data', pubsub_message['message'])
        self.assertIn('messageId', pubsub_message['message'])

if __name__ == '__main__':
    # Run the tests
    print("🧪 Running End-to-End Email Processing Pipeline Tests")
    print("=" * 60)
    
    unittest.main(verbosity=2)
